from langchain_community.document_loaders import WebBaseLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain.tools.retriever import create_retriever_tool
from langchain_ollama import OllamaEmbeddings
from langgraph.graph import MessagesState
from langchain.chat_models import init_chat_model

from dotenv import load_dotenv
import os


load_dotenv()  # 自动加载 .env 文件

api_key = os.environ["OPENAI_API_KEY"]
api_base = os.environ["OPENAI_API_BASE"]
model_name = os.environ["OPENAI_MODEL_NAME"]
emb_model_name = os.environ["OPENAI_EMB_MODEL_NAME"]

urls = [
    "https://lilianweng.github.io/posts/2024-11-28-reward-hacking/",
    "https://lilianweng.github.io/posts/2024-07-07-hallucination/",
    "https://lilianweng.github.io/posts/2024-04-12-diffusion-video/",
]

response_model = init_chat_model("openai:qwen3:0.6b", temperature=0.6)

embed = OllamaEmbeddings(
    model="snowflake-arctic-embed2:latest"
)


def load_and_split_documents(urls: list[str], chunk_size: int = 100, chunk_overlap: int = 50):
    docs = [WebBaseLoader(url).load() for url in urls]
    docs_list = [item for sublist in docs for item in sublist]
    text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        chunk_size=chunk_size, chunk_overlap=chunk_overlap
    )
    return text_splitter.split_documents(docs_list)



def generate_query_or_respond(state: MessagesState):
    """Call the model to generate a response based on the current state. Given
    the question, it will decide to retrieve using the retriever tool, or simply respond to the user.
    """
    response = (
        response_model
        .bind_tools([retriever_tool]).invoke(state["messages"])
    )
    return {"messages": [response]}

from pydantic import BaseModel, Field
from typing import Literal

GRADE_PROMPT = (
    "You are a grader assessing relevance of a retrieved document to a user question. \n "
    "Here is the retrieved document: \n\n {context} \n\n"
    "Here is the user question: {question} \n"
    "If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \n"
    "Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question."
)


class GradeDocuments(BaseModel):
    """Grade documents using a binary score for relevance check."""

    binary_score: str = Field(
        description="Relevance score: 'yes' if relevant, or 'no' if not relevant"
    )


grader_model = init_chat_model("openai:qwen3:0.6b", temperature=0)


def grade_documents(
    state: MessagesState,
) -> Literal["generate_answer", "rewrite_question"]:
    """Determine whether the retrieved documents are relevant to the question."""
    question = state["messages"][0].content
    context = state["messages"][-1].content

    prompt = GRADE_PROMPT.format(question=question, context=context)
    response = (
        grader_model
        .with_structured_output(GradeDocuments).invoke(
            [{"role": "user", "content": prompt}]
        )
    )
    score = response.binary_score

    if score == "yes":
        return "generate_answer"
    else:
        return "rewrite_question"

# 1. 问题重写节点
REWRITE_PROMPT = (
    "Look at the input and try to reason about the underlying semantic intent / meaning.\n"
    "Here is the initial question:"
    "\n ------- \n"
    "{question}"
    "\n ------- \n"
    "Formulate an improved question:"
)

def rewrite_question(state: MessagesState):
    """重写用户原始问题，提升检索相关性。"""
    messages = state["messages"]
    question = messages[0].content
    prompt = REWRITE_PROMPT.format(question=question)
    response = response_model.invoke([{"role": "user", "content": prompt}])
    return {"messages": [{"role": "user", "content": response.content}]}

# 2. 生成最终答案节点
GENERATE_PROMPT = (
    "You are an assistant for question-answering tasks. "
    "Use the following pieces of retrieved context to answer the question. "
    "If you don't know the answer, just say that you don't know. "
    "Use three sentences maximum and keep the answer concise.\n"
    "Question: {question} \n"
    "Context: {context}"
)

def generate_answer(state: MessagesState):
    """根据检索到的上下文和原始问题生成最终答案。"""
    question = state["messages"][0].content
    context = state["messages"][-1].content
    prompt = GENERATE_PROMPT.format(question=question, context=context)
    response = response_model.invoke([{"role": "user", "content": prompt}])
    return {"messages": [response]}

# 3. 组装 StateGraph 工作流
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode, tools_condition

def build_workflow(retriever_tool):
    workflow = StateGraph(MessagesState)
    # 注册节点
    workflow.add_node("generate_query_or_respond", generate_query_or_respond)
    workflow.add_node("retrieve", ToolNode([retriever_tool]))
    workflow.add_node("rewrite_question", rewrite_question)
    workflow.add_node("generate_answer", generate_answer)
    # 边
    workflow.add_edge(START, "generate_query_or_respond")
    workflow.add_conditional_edges(
        "generate_query_or_respond",
        tools_condition,
        {
            "tools": "retrieve",
            END: END,
        },
    )
    workflow.add_conditional_edges(
        "retrieve",
        grade_documents,
    )
    workflow.add_edge("generate_answer", END)
    workflow.add_edge("rewrite_question", "generate_query_or_respond")
    return workflow.compile()

# 4. 主流程，流式运行

def main():
    print("Hello from agnetic-rag!")
    doc_splits = load_and_split_documents(urls)
    vectorstore = InMemoryVectorStore.from_documents(
        documents=doc_splits, embedding=embed
    )
    retriever = vectorstore.as_retriever()
    retriever_tool = create_retriever_tool(
        retriever,
        "retrieve_blog_posts",
        "Search and return information about Lilian Weng blog posts.",
    )
    # 重新绑定 retriever_tool 到 generate_query_or_respond
    def generate_query_or_respond_with_tool(state: MessagesState):
        response = (
            response_model
            .bind_tools([retriever_tool]).invoke(state["messages"])
        )
        return {"messages": [response]}
    # 用新函数替换
    global generate_query_or_respond
    generate_query_or_respond = generate_query_or_respond_with_tool

    # 构建工作流
    graph = build_workflow(retriever_tool)
    # 用户输入
    user_input = {
        "messages": [
            {
                "role": "user",
                "content": "What does Lilian Weng say about types of reward hacking?",
            }
        ]
    }
    # 流式运行
    for chunk in graph.stream(user_input):
        for node, update in chunk.items():
            print(f"Update from node {node}")
            if "messages" in update and update["messages"]:
                msg = update["messages"][-1]
                if hasattr(msg, "pretty_print"):
                    msg.pretty_print()
                else:
                    print(msg)
            print("\n\n")

def test_agentic_rag():
    """
    测试 Agentic RAG 工作流主流程，自动验证多组输入的输出合理性。
    """
    print("=== 开始 Agentic RAG 测试 ===")
    doc_splits = load_and_split_documents(urls)
    vectorstore = InMemoryVectorStore.from_documents(
        documents=doc_splits, embedding=embed
    )
    retriever = vectorstore.as_retriever()
    retriever_tool = create_retriever_tool(
        retriever,
        "retrieve_blog_posts",
        "Search and return information about Lilian Weng blog posts.",
    )
    def generate_query_or_respond_with_tool(state: MessagesState):
        response = (
            response_model
            .bind_tools([retriever_tool]).invoke(state["messages"])
        )
        return {"messages": [response]}
    global generate_query_or_respond
    generate_query_or_respond = generate_query_or_respond_with_tool

    graph = build_workflow(retriever_tool)

    test_cases = [
        "What does Lilian Weng say about types of reward hacking?",
        "请总结一下Lilian Weng关于幻觉（hallucination）的观点。",
        "Diffusion video的核心思想是什么？",
        "你好！",
    ]
    for idx, question in enumerate(test_cases):
        print(f"\n--- 测试用例 {idx+1}: {question} ---")
        user_input = {"messages": [{"role": "user", "content": question}]}
        for chunk in graph.stream(user_input):
            for node, update in chunk.items():
                print(f"Update from node {node}")
                if "messages" in update and update["messages"]:
                    msg = update["messages"][-1]
                    if hasattr(msg, "pretty_print"):
                        msg.pretty_print()
                    else:
                        print(msg)
                print("\n")

# 1. 文档加载、向量库、retriever_tool 初始化
doc_splits = load_and_split_documents(urls)
vectorstore = InMemoryVectorStore.from_documents(
    documents=doc_splits, embedding=embed
)
retriever = vectorstore.as_retriever()
retriever_tool = create_retriever_tool(
    retriever,
    "retrieve_blog_posts",
    "Search and return information about Lilian Weng blog posts.",
)

def generate_query_or_respond_with_tool(state: MessagesState):
    response = (
        response_model
        .bind_tools([retriever_tool]).invoke(state["messages"])
    )
    return {"messages": [response]}

generate_query_or_respond = generate_query_or_respond_with_tool

# 2. 构建 workflow
graph = build_workflow(retriever_tool)

# 3. 保持 test_agentic_rag/main 作为测试入口
if __name__ == "__main__":
    test_agentic_rag()
