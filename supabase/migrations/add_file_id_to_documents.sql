-- Migration: Add file_id column to documents table for better chunk organization
-- This allows direct querying of chunks by parent document ID

-- Add file_id column
ALTER TABLE public.documents 
ADD COLUMN IF NOT EXISTS file_id TEXT;

-- Create index for fast file_id lookups
CREATE INDEX IF NOT EXISTS documents_file_id_idx 
ON public.documents (file_id) 
TABLESPACE pg_default;

-- Update existing records to populate file_id from metadata
UPDATE public.documents 
SET file_id = metadata->>'id' 
WHERE file_id IS NULL AND metadata->>'id' IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.documents.file_id IS 'Parent document/file ID for grouping chunks together';

-- Optional: Add constraint to ensure file_id is not null for new records
-- ALTER TABLE public.documents 
-- ADD CONSTRAINT documents_file_id_not_null 
-- CHECK (file_id IS NOT NULL);