-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('business', 'personal')),
    priority TEXT NOT NULL CHECK (priority IN ('high', 'medium', 'low')),
    status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'processing')),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT NOT NULL,
    total_size TEXT,
    total_documents INTEGER DEFAULT 0,
    total_subcategories INTEGER DEFAULT 0,
    tags TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subcategories table
CREATE TABLE IF NOT EXISTS subcategories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('business', 'personal')),
    category_id TEXT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    priority TEXT NOT NULL CHECK (priority IN ('high', 'medium', 'low')),
    status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'processing')),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    size TEXT,
    compression INTEGER CHECK (compression >= 0 AND compression <= 100),
    relevance_score REAL CHECK (relevance_score >= 0.0 AND relevance_score <= 1.0),
    tags TEXT[] DEFAULT '{}',
    description TEXT NOT NULL,
    source TEXT NOT NULL,
    knowledge_graph BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create context_documents table (renamed to avoid conflict with existing documents table)
CREATE TABLE IF NOT EXISTS context_documents (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT,
    description TEXT,
    create_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    size TEXT,
    tags TEXT[] DEFAULT '{}',
    source TEXT NOT NULL,
    subcategory_id TEXT NOT NULL REFERENCES subcategories(id) ON DELETE CASCADE,
    content TEXT,
    metadata JSONB DEFAULT '{}',
    is_vectorized BOOLEAN DEFAULT FALSE,
    vectorized_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_status ON categories(status);
CREATE INDEX IF NOT EXISTS idx_categories_priority ON categories(priority);
CREATE INDEX IF NOT EXISTS idx_categories_tags ON categories USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_subcategories_category_id ON subcategories(category_id);
CREATE INDEX IF NOT EXISTS idx_subcategories_type ON subcategories(type);
CREATE INDEX IF NOT EXISTS idx_subcategories_status ON subcategories(status);
CREATE INDEX IF NOT EXISTS idx_subcategories_tags ON subcategories USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_context_documents_subcategory_id ON context_documents(subcategory_id);
CREATE INDEX IF NOT EXISTS idx_context_documents_tags ON context_documents USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_context_documents_content ON context_documents USING GIN(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_context_documents_name ON context_documents USING GIN(to_tsvector('english', name));

-- Create triggers to update last_updated timestamps
CREATE OR REPLACE FUNCTION update_last_updated_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_categories_last_updated BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_last_updated_column();

CREATE TRIGGER update_subcategories_last_updated BEFORE UPDATE ON subcategories
    FOR EACH ROW EXECUTE FUNCTION update_last_updated_column();

CREATE TRIGGER update_context_documents_last_updated BEFORE UPDATE ON context_documents
    FOR EACH ROW EXECUTE FUNCTION update_last_updated_column();