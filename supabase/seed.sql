-- Seed script to initialize database with mock data
-- This script should be run after the migration

-- Clear existing data
DELETE FROM context_documents;
DELETE FROM subcategories;
DELETE FROM categories;

-- Insert Categories
INSERT INTO categories (id, name, type, priority, status, description, total_size, total_documents, total_subcategories, tags, metadata) VALUES
('cat_business_rd', '研发设计', 'business', 'high', 'active', '企业研发设计相关的文档资源，包括产品设计、技术方案等', '7.0 MB', 45, 2, ARRAY['研发', '设计', '技术方案', '产品开发'], '{"department": "研发部", "accessLevel": "internal", "updateFrequency": "weekly", "reviewCycle": "monthly"}'),
('cat_business_production', '生产管控', 'business', 'high', 'active', '生产制造过程中的管控文档，包括工艺流程、质量管理等', '5.9 MB', 67, 2, ARRAY['生产管控', '工艺流程', '质量管理', '制造'], '{"department": "生产部", "accessLevel": "restricted", "updateFrequency": "daily", "reviewCycle": "weekly"}'),
('cat_business_operations', '运维数据', 'business', 'high', 'active', '设备运维和生产运营相关的数据文档', '5.8 MB', 89, 2, ARRAY['运维', '设备维护', '运营监控', '实时数据'], '{"department": "运维部", "accessLevel": "operational", "updateFrequency": "realtime", "reviewCycle": "daily"}'),
('cat_personal_academic', '学术论文', 'personal', 'high', 'active', '收集的学术研究论文，涵盖AI、工业4.0等前沿技术领域', '20.8 MB', 156, 2, ARRAY['学术论文', '研究', 'AI', '工业4.0'], '{"collector": "个人", "accessLevel": "academic", "updateFrequency": "weekly", "reviewCycle": "monthly"}'),
('cat_personal_web', '网络资源', 'personal', 'medium', 'active', '从网络收集的优质技术资源，包括博客、开源项目、学习资料等', '12.9 MB', 234, 3, ARRAY['网络资源', '技术博客', '开源项目', '学习资料'], '{"collector": "个人", "accessLevel": "public", "updateFrequency": "daily", "reviewCycle": "weekly"}'),
('cat_personal_note', '个人笔记', 'personal', 'medium', 'active', '个人学习、随笔与知识整理', '3.2 MB', 7, 1, ARRAY['笔记', '随笔', '学习记录'], '{"collector": "个人", "accessLevel": "private"}'),
('cat_personal_other', '其他资料', 'personal', 'low', 'active', '其他未分类个人资料', '0.8 MB', 2, 1, ARRAY['其他'], '{"collector": "个人", "accessLevel": "private"}');

-- Insert SubCategories (sample data)
INSERT INTO subcategories (id, name, type, category_id, priority, status, size, compression, relevance_score, tags, description, source, knowledge_graph, metadata) VALUES
('sc_business_design', '研发设计', 'business', 'cat_business_rd', 'high', 'active', '4.2 MB', 88, 0.96, ARRAY['产品设计', '设计规范', 'CAD标准', '材料选择'], '包含产品设计标准、CAD建模规范、材料特性数据库和设计验证准则', '设计工程部', true, '{"designRules": 234, "materials": 156, "cadTemplates": 89, "validationCases": 67, "totalDocuments": 3, "department": "设计工程部"}'),
('sc_business_production', '生产管控', 'business', 'cat_business_rd', 'high', 'active', '3.5 MB', 85, 0.93, ARRAY['生产管理', '流程优化', '质量控制'], '生产流程、管控标准与质量管理文档', '生产部', true, '{"processes": 45, "qualityControls": 23, "totalDocuments": 5, "department": "生产部"}'),
('sc_business_mro', '运维数据', 'business', 'cat_business_rd', 'medium', 'active', '2.1 MB', 80, 0.90, ARRAY['运维', '数据监控', '设备管理'], '设备运维、监控与数据分析文档', '运维部', true, '{"devices": 67, "monitoring": 34, "totalDocuments": 4, "department": "运维部"}'),
('sc_business_standard', '企业标准', 'business', 'cat_business_rd', 'medium', 'active', '1.8 MB', 78, 0.89, ARRAY['标准', '规范', '流程'], '企业内部标准与规范文档', '标准化部', true, '{"standards": 12, "procedures": 8, "totalDocuments": 2, "department": "标准化部"}'),
('sc_business_other', '其他文档', 'business', 'cat_business_rd', 'low', 'active', '0.5 MB', 70, 0.80, ARRAY['其他'], '其他未分类业务文档', '综合部', true, '{"totalDocuments": 1, "department": "综合部"}'),
('sc_personal_paper', '学术论文', 'personal', 'cat_personal_academic', 'high', 'active', '12.5 MB', 75, 0.92, ARRAY['人工智能', '机器学习', '深度学习', '论文研究'], '收集的AI领域重要论文，包括机器学习、深度学习和自然语言处理', '学术期刊', true, '{"totalPapers": 89, "totalAuthors": 156, "totalCitations": 234000, "topVenues": ["NeurIPS", "ICML", "ICLR", "ACL"], "totalDocuments": 9, "source": "学术期刊"}'),
('sc_personal_blogs', '网络资源', 'personal', 'cat_personal_web', 'medium', 'active', '8.1 MB', 70, 0.85, ARRAY['技术博客', '开发经验', '问题解决', '技术分享'], '收藏的优质技术博客文章和开发经验分享', '网络收集', true, '{"totalBlogs": 234, "totalAuthors": 89, "totalTopics": 45, "totalBookmarks": 156, "avgReadTime": "15 min", "lastCrawled": "2024-01-06", "totalDocuments": 15, "source": "网络收集"}'),
('sc_personal_note', '个人笔记', 'personal', 'cat_personal_note', 'medium', 'active', '3.2 MB', 72, 0.83, ARRAY['笔记', '随笔', '学习记录'], '个人学习、随笔与知识整理', '个人', true, '{"totalNotes": 56, "totalDocuments": 7, "owner": "个人"}'),
('sc_personal_other', '其他资料', 'personal', 'cat_personal_other', 'low', 'active', '0.8 MB', 65, 0.78, ARRAY['其他'], '其他未分类个人资料', '个人', true, '{"totalDocuments": 2, "owner": "个人"}');

-- Note: Document insertion would be very long, so we'll create a separate script for that
-- The documents can be inserted using the Python script that reads from mock_data