# Supabase 向量存储集成指南

## 概述

本项目已成功集成了 Supabase 向量存储功能，使用 pgvector 扩展来存储和检索文档向量。这替代了之前的内存向量存储，提供了持久化的数据存储能力。

## 功能特性

- ✅ 使用 pgvector 扩展进行向量存储
- ✅ 支持文档的增删改查操作
- ✅ 基于余弦相似度的语义搜索
- ✅ 数据持久化存储
- ✅ 与现有 RAG 系统无缝集成
- ✅ 支持回退到内存存储

## 环境要求

### 1. Supabase 本地服务

确保 Supabase 本地服务正在运行：

```bash
supabase start
```

### 2. Python 依赖

使用 uv 安装依赖：

```bash
uv sync
```

主要新增依赖：
- `supabase>=2.9.1` - Supabase Python 客户端
- `psycopg2-binary>=2.9.9` - PostgreSQL 数据库连接器

### 3. 环境变量

`.env` 文件中已配置：

```env
# Supabase Configuration
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_DB_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
```

## 数据库设置

### 1. pgvector 扩展

已通过迁移文件自动启用：

```sql
-- supabase/migrations/20240101000000_enable_pgvector.sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. 数据表结构

```sql
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1024), -- 根据嵌入模型维度调整
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. 索引优化

- 向量相似性搜索索引：`ivfflat (embedding vector_cosine_ops)`
- 元数据查询索引：`GIN (metadata)`
- 全文搜索索引：`GIN (to_tsvector('english', content))`

## 使用方法

### 1. 基本使用

```python
from src.document_processor import create_vector_store_and_retriever
from langchain_core.documents import Document

# 创建测试文档
docs = [
    Document(
        page_content="LangChain是一个用于构建LLM应用的框架",
        metadata={"source": "guide", "type": "framework"}
    )
]

# 使用 Supabase 向量存储
vectorstore, retriever, retriever_tool = create_vector_store_and_retriever(
    doc_splits=docs,
    use_supabase=True,  # 启用 Supabase
    collection_name="my_documents"  # 表名
)

# 搜索相关文档
results = retriever.invoke("什么是LangChain？")
```

### 2. 直接使用 SupabaseVectorStore

```python
from src.supabase_vectorstore import SupabaseVectorStore
from src.models import get_embeddings

embeddings = get_embeddings()

# 创建向量存储
vectorstore = SupabaseVectorStore(
    embedding=embeddings,
    table_name="custom_docs"
)

# 添加文档
vectorstore.add_texts(
    texts=["文档内容1", "文档内容2"],
    metadatas=[{"source": "doc1"}, {"source": "doc2"}]
)

# 搜索
results = vectorstore.similarity_search("查询内容", k=5)
```

### 3. 配置选项

在 `create_vector_store_and_retriever` 函数中：

- `use_supabase=True`: 使用 Supabase 向量存储
- `use_supabase=False`: 使用内存向量存储（默认回退）
- `collection_name`: 指定数据表名称

## 测试验证

### 1. 运行完整测试

```bash
uv run python test_supabase_vectorstore.py
```

### 2. 运行简单演示

```bash
uv run python simple_test.py
```

### 3. 验证数据持久化

```bash
uv run python verify_supabase_data.py
```

### 4. 查看使用示例

```bash
uv run python example_supabase_usage.py
```

## API 参考

### SupabaseVectorStore 类

#### 构造函数
```python
SupabaseVectorStore(
    embedding: Embeddings,
    table_name: str = "documents"
)
```

#### 主要方法

- `add_texts(texts, metadatas, ids)`: 添加文本
- `similarity_search(query, k)`: 相似性搜索
- `similarity_search_with_score(query, k)`: 带分数的搜索
- `delete(ids)`: 删除文档
- `clear()`: 清空所有文档
- `get_document_count()`: 获取文档数量

#### 类方法

- `from_texts(texts, embedding, metadatas, table_name)`: 从文本创建
- `from_documents(documents, embedding, table_name)`: 从文档创建

## 性能优化

### 1. 索引优化

- 使用 IVFFlat 索引进行向量搜索
- 根据数据量调整 `lists` 参数
- 定期运行 `VACUUM` 和 `ANALYZE`

### 2. 查询优化

- 使用余弦相似度 (`<=>`) 操作符
- 限制返回结果数量 (`LIMIT`)
- 合理设置嵌入维度

### 3. 连接池

考虑使用连接池来优化数据库连接：

```python
# 可以在未来版本中添加连接池支持
from psycopg2 import pool
```

## 故障排除

### 1. 常见问题

**pgvector 扩展未安装**
```bash
# 重新运行迁移
supabase db reset
```

**连接失败**
```bash
# 检查 Supabase 服务状态
supabase status
```

**嵌入维度不匹配**
- 检查嵌入模型的输出维度
- 更新迁移文件中的 `VECTOR(dimension)` 定义

### 2. 调试技巧

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

直接查询数据库：

```sql
-- 检查数据
SELECT COUNT(*) FROM documents;
SELECT id, content, metadata FROM documents LIMIT 5;

-- 检查向量维度
SELECT array_length(embedding, 1) as dimension FROM documents LIMIT 1;
```

## 下一步

1. **生产环境部署**: 配置生产环境的 Supabase 实例
2. **性能监控**: 添加查询性能监控
3. **批量操作**: 实现批量插入和更新
4. **备份策略**: 设置数据备份和恢复策略
5. **安全加固**: 配置行级安全策略 (RLS)

## 总结

Supabase 向量存储已成功集成到你的 RAG 系统中，提供了：

- 🚀 高性能的向量搜索
- 💾 持久化的数据存储
- 🔧 灵活的配置选项
- 🛡️ 可靠的错误处理
- 📈 良好的扩展性

现在你可以在生产环境中使用这个强大的向量存储解决方案了！