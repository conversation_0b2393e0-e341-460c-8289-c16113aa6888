from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.routers.context import router as context_router
from src.routers.rag import router as rag_router
from src.routers.document import router as document_router
from src.routers.chunks import router as chunks_router

app = FastAPI(title="Unified API", description="API for document management and knowledge graph", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(context_router)
app.include_router(rag_router)
app.include_router(document_router)
app.include_router(chunks_router)

@app.get("/")
async def root():
    return {"message": "Unified API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
