"""Main RAG service class for easy integration with FastAPI."""

from typing import List, Dict, Any, Iterator
from .document_processor import load_and_split_documents, create_vector_store_and_retriever, get_urls_from_context
from .workflow import build_workflow
from .config import config
from .supabase_vectorstore import SupabaseVectorStore


class AgenticRAGService:
    """Main service class for Agentic RAG functionality."""
    
    def __init__(self, urls: List[str] = None):
        """Initialize the RAG service."""
        self.urls = urls or get_urls_from_context()
        self.vectorstore = None
        self.retriever = None
        self.retriever_tool = None
        self.graph = None
        self._initialize()
    
    def _initialize(self):
        """Initialize the RAG components from existing Supabase data."""
        print("Connecting to existing Supabase vector store...")
        
        try:
            # 直接创建Supabase向量存储实例，连接到现有数据
            from .models import get_embeddings
            embeddings = get_embeddings()
            
            # 创建向量存储实例（不添加新文档，只连接到现有表）
            self.vectorstore = SupabaseVectorStore(
                embedding=embeddings,
                table_name="document_embedding"  # 使用新表名
            )
            
            # 检查现有文档数量
            doc_count = self.vectorstore.get_document_count()
            print(f"成功连接到Supabase向量存储，发现 {doc_count} 个现有文档")
            
            if doc_count == 0:
                print("警告：向量存储中没有找到文档，请确认数据已正确导入")
            
            # 创建检索器
            print("Creating retriever from existing vector store...")
            self.retriever = self.vectorstore.as_retriever()
            
            # 创建检索工具
            from langchain.tools.retriever import create_retriever_tool
            self.retriever_tool = create_retriever_tool(
                self.retriever,
                "retrieve_documents",
                "Search and return information from the document knowledge base.",
            )
            
            print("Building workflow...")
            self.graph = build_workflow(self.retriever_tool)
            
            print("RAG service initialized successfully from existing Supabase data!")
            
        except Exception as e:
            print(f"Error initializing RAG service: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def query(self, question: str) -> Iterator[Dict[str, Any]]:
        """Process a query and return streaming results."""
        user_input = {
            "messages": [
                {"role": "user", "content": question}
            ]
        }
        
        for chunk in self.graph.stream(user_input):
            yield chunk
    
    def query_sync(self, question: str) -> Dict[str, Any]:
        """Process a query and return the final result."""
        results = list(self.query(question))
        return results[-1] if results else {}
    
    def reload_documents(self, urls: List[str] = None):
        """Reload documents with new URLs."""
        try:
            print(f"Reloading documents with {len(urls) if urls else 0} URLs...")
            
            # Filter out empty URLs and use real URLs
            if urls:
                valid_urls = [url for url in urls if url and url.startswith('http')]
                if valid_urls:
                    self.urls = valid_urls
                    print(f"Using {len(valid_urls)} valid URLs from request")
                else:
                    print("No valid URLs provided, using default URLs")
            
            # Clear existing documents from the vector store if it's a Supabase store
            if hasattr(self, 'vectorstore') and isinstance(self.vectorstore, SupabaseVectorStore):
                print("Clearing existing documents from Supabase...")
                try:
                    self.vectorstore.clear()
                    print("Successfully cleared existing documents")
                except Exception as e:
                    print(f"Error clearing documents: {e}")
            
            # Reinitialize with new URLs
            self._initialize()
            print("Documents reloaded successfully")
            
        except Exception as e:
            print(f"Error reloading documents: {e}")
            import traceback
            traceback.print_exc()
            raise


# Global service instance for easy import
rag_service = None

def get_rag_service() -> AgenticRAGService:
    """Get or create the global RAG service instance."""
    global rag_service
    if rag_service is None:
        rag_service = AgenticRAGService()
    return rag_service