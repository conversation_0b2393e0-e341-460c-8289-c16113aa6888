"""Workflow node functions for the Agentic RAG system."""

from typing import Literal
from langgraph.graph import MessagesState
from .models import get_response_model, get_grader_model, GradeDocuments
from .prompts import GRADE_PROMPT, REWRITE_PROMPT, GENERATE_PROMPT


class WorkflowNodes:
    """Container for workflow node functions."""
    
    def __init__(self, retriever_tool):
        self.retriever_tool = retriever_tool
        self.response_model = get_response_model()
        self.grader_model = get_grader_model()
    
    def generate_query_or_respond(self, state: MessagesState):
        """Call the model to generate a response based on the current state. 
        Given the question, it will decide to retrieve using the retriever tool, 
        or simply respond to the user.
        """
        response = (
            self.response_model
            .bind_tools([self.retriever_tool]).invoke(state["messages"])
        )
        return {"messages": [response]}
    
    def grade_documents(self, state: MessagesState) -> Literal["generate_answer", "rewrite_question"]:
        """Determine whether the retrieved documents are relevant to the question."""
        question = state["messages"][0].content
        context = state["messages"][-1].content

        prompt = GRADE_PROMPT.format(question=question, context=context)
        response = (
            self.grader_model
            .with_structured_output(GradeDocuments).invoke(
                [{"role": "user", "content": prompt}]
            )
        )
        score = response.binary_score

        if score == "yes":
            return "generate_answer"
        else:
            return "rewrite_question"
    
    def rewrite_question(self, state: MessagesState):
        """重写用户原始问题，提升检索相关性。"""
        messages = state["messages"]
        question = messages[0].content
        prompt = REWRITE_PROMPT.format(question=question)
        response = self.response_model.invoke([{"role": "user", "content": prompt}])
        return {"messages": [{"role": "user", "content": response.content}]}
    
    def generate_answer(self, state: MessagesState):
        """根据检索到的上下文和原始问题生成最终答案。"""
        question = state["messages"][0].content
        context = state["messages"][-1].content
        prompt = GENERATE_PROMPT.format(question=question, context=context)
        response = self.response_model.invoke([{"role": "user", "content": prompt}])
        return {"messages": [response]}