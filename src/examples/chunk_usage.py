"""Example usage of the chunk service."""

import asyncio
from src.chunk_service import chunk_service
from src.document_processor import get_document_chunks, get_document_chunk_summary


async def demo_chunk_service():
    """Demonstrate chunk service functionality."""
    
    # Example file ID from your metadata
    file_id = "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"
    
    print("=== Chunk Service Demo ===\n")
    
    try:
        # 1. Get document summary
        print("1. Getting document summary...")
        summary = await get_document_chunk_summary(file_id)
        print(f"Summary: {summary}\n")
        
        # 2. Get all chunks using new file_id approach (after migration)
        print("2. Getting chunks using file_id column...")
        chunks_new = await get_document_chunks(file_id, use_file_id=True)
        print(f"Found {len(chunks_new)} chunks using file_id column\n")
        
        # 3. Get all chunks using legacy metadata approach
        print("3. Getting chunks using metadata->id...")
        chunks_legacy = await get_document_chunks(file_id, use_file_id=False)
        print(f"Found {len(chunks_legacy)} chunks using metadata approach\n")
        
        # 4. Show first few chunks
        if chunks_legacy:
            print("4. Sample chunks:")
            for i, chunk in enumerate(chunks_legacy[:3]):
                print(f"   Chunk {i+1}:")
                print(f"   - ID: {chunk['id']}")
                print(f"   - Index: {chunk['metadata'].get('chunk_index', 'N/A')}")
                print(f"   - Content preview: {chunk['content'][:100]}...")
                print(f"   - Title: {chunk['metadata'].get('title', 'N/A')}")
                print()
        
        # 5. Search chunks by content
        print("5. Searching chunks...")
        search_results = await chunk_service.search_chunks_by_content(
            "Google Gemini", file_id=file_id, limit=3
        )
        print(f"Found {len(search_results)} chunks matching 'Google Gemini'")
        for result in search_results:
            print(f"   - Chunk {result['metadata'].get('chunk_index')}: {result['content'][:80]}...")
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        await chunk_service.close()


if __name__ == "__main__":
    asyncio.run(demo_chunk_service())