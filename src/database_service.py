from typing import List, Optional, Dict, Any
import asyncpg
from src.config import config
from src.schemas.context import Category, SubCategory, Document
import json

class DatabaseService:
    """Database service for managing categories, subcategories, and documents."""
    
    def __init__(self):
        self.db_url = config.SUPABASE_DB_URL
        self._pool = None
    
    async def get_pool(self):
        """Get or create database connection pool."""
        if self._pool is None:
            self._pool = await asyncpg.create_pool(self.db_url)
        return self._pool
    
    async def close(self):
        """Close database connection pool."""
        if self._pool:
            await self._pool.close()
    
    # Category CRUD operations
    async def create_category(self, category: Category) -> Category:
        """Create a new category."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO categories (id, name, type, priority, status, description, 
                                      total_size, total_documents, total_subcategories, tags, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                """,
                category.id, category.name, category.type, category.priority, category.status,
                category.description, getattr(category, 'totalSize', None), 
                getattr(category, 'totalDocuments', 0), getattr(category, 'totalSubcategories', 0),
                category.tags, json.dumps(category.metadata or {})
            )
        return category
    
    async def get_category(self, category_id: str) -> Optional[Category]:
        """Get category by ID."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM categories WHERE id = $1", category_id
            )
            if row:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                return Category(
                    id=row['id'],
                    name=row['name'],
                    type=row['type'],
                    priority=row['priority'],
                    status=row['status'],
                    lastUpdated=row['last_updated'].isoformat(),
                    description=row['description'],
                    tags=row['tags'] or [],
                    metadata=metadata or {}
                )
        return None

    async def get_all_categories(self) -> List[Category]:
        """Get all categories."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            rows = await conn.fetch("SELECT * FROM categories ORDER BY name")
            categories = []
            for row in rows:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                categories.append(Category(
                    id=row['id'],
                    name=row['name'],
                    type=row['type'],
                    priority=row['priority'],
                    status=row['status'],
                    lastUpdated=row['last_updated'].isoformat(),
                    description=row['description'],
                    tags=row['tags'] or [],
                    metadata=metadata or {}
                ))
            return categories

    async def get_subcategory(self, subcategory_id: str) -> Optional[SubCategory]:
        """Get subcategory by ID."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM subcategories WHERE id = $1", subcategory_id
            )
            if row:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                return SubCategory(
                    id=row['id'],
                    name=row['name'],
                    type=row['type'],
                    categoryId=row['category_id'],
                    priority=row['priority'],
                    status=row['status'],
                    lastUpdated=row['last_updated'].isoformat(),
                    size=row['size'],
                    compression=row['compression'],
                    relevanceScore=row['relevance_score'],
                    tags=row['tags'] or [],
                    description=row['description'],
                    source=row['source'],
                    knowledgeGraph=row['knowledge_graph'],
                    metadata=metadata or {}
                )
        return None

    async def get_subcategories_by_category(self, category_id: str) -> List[SubCategory]:
        """Get all subcategories for a category."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            rows = await conn.fetch(
                "SELECT * FROM subcategories WHERE category_id = $1 ORDER BY name", 
                category_id
            )
            subcategories = []
            for row in rows:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                subcategories.append(SubCategory(
                    id=row['id'],
                    name=row['name'],
                    type=row['type'],
                    categoryId=row['category_id'],
                    priority=row['priority'],
                    status=row['status'],
                    lastUpdated=row['last_updated'].isoformat(),
                    size=row['size'],
                    compression=row['compression'],
                    relevanceScore=row['relevance_score'],
                    tags=row['tags'] or [],
                    description=row['description'],
                    source=row['source'],
                    knowledgeGraph=row['knowledge_graph'],
                    metadata=metadata or {}
                ))
            return subcategories

    async def get_document(self, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM context_documents WHERE id = $1", document_id
            )
            if row:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                return Document(
                    id=row['id'],
                    name=row['name'],
                    url=row['url'],
                    description=row['description'],
                    createAt=row['create_at'].isoformat(),
                    lastUpdated=row['last_updated'].isoformat(),
                    size=row['size'],
                    tags=row['tags'] or [],
                    source=row['source'],
                    subcategoryId=row['subcategory_id'],
                    content=row['content'],
                    metadata=metadata or {}
                )
        return None
    
    async def get_documents_by_subcategory(self, subcategory_id: str) -> List[Document]:
        """Get all documents for a subcategory."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            rows = await conn.fetch(
                "SELECT * FROM context_documents WHERE subcategory_id = $1 ORDER BY name", 
                subcategory_id
            )
            documents = []
            for row in rows:
                # Parse metadata JSON string to dict
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                documents.append(Document(
                    id=row['id'],
                    name=row['name'],
                    url=row['url'],
                    description=row['description'],
                    createAt=row['create_at'].isoformat(),
                    lastUpdated=row['last_updated'].isoformat(),
                    size=row['size'],
                    tags=row['tags'] or [],
                    source=row['source'],
                    subcategoryId=row['subcategory_id'],
                    content=row['content'],
                    metadata=metadata or {}
                ))
            return documents

    async def create_document(self, document: Document) -> Document:
        """Create a new document."""
        from datetime import datetime
        
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Convert string dates to datetime objects if needed
            create_at = document.createAt
            last_updated = document.lastUpdated
            
            if isinstance(create_at, str):
                create_at = datetime.strptime(create_at, "%Y-%m-%d %H:%M:%S")
            if isinstance(last_updated, str):
                last_updated = datetime.strptime(last_updated, "%Y-%m-%d %H:%M:%S")
                
            await conn.execute(
                """
                INSERT INTO context_documents (id, name, url, description, create_at, last_updated, 
                                              size, tags, source, subcategory_id, content, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                """,
                document.id, document.name, document.url, document.description,
                create_at, last_updated, document.size,
                document.tags, document.source, document.subcategoryId,
                document.content, json.dumps(document.metadata or {})
            )
        return document

    async def update_document(self, document_id: str, updates: Dict[str, Any]) -> Optional[Document]:
        """Update document."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Build dynamic update query
            set_clauses = []
            values = []
            param_count = 1
            
            for key, value in updates.items():
                if key == 'metadata':
                    set_clauses.append(f"metadata = ${param_count}")
                    values.append(json.dumps(value))
                elif key == 'tags':
                    set_clauses.append(f"tags = ${param_count}")
                    values.append(value)
                else:
                    # Convert camelCase to snake_case
                    db_key = key.replace('lastUpdated', 'last_updated').replace('createAt', 'create_at').replace('subcategoryId', 'subcategory_id')
                    set_clauses.append(f"{db_key} = ${param_count}")
                    values.append(value)
                param_count += 1
            
            if set_clauses:
                values.append(document_id)
                query = f"UPDATE context_documents SET {', '.join(set_clauses)} WHERE id = ${param_count}"
                await conn.execute(query, *values)
                return await self.get_document(document_id)
        return None
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete document."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            result = await conn.execute("DELETE FROM context_documents WHERE id = $1", document_id)
            return result == "DELETE 1"
    
    async def update_subcategory(self, subcategory_id: str, updates: Dict[str, Any]) -> Optional[SubCategory]:
        """Update subcategory."""
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Build dynamic update query
            set_clauses = []
            values = []
            param_count = 1
            
            for key, value in updates.items():
                if key == 'metadata':
                    set_clauses.append(f"metadata = ${param_count}")
                    values.append(json.dumps(value))
                elif key == 'tags':
                    set_clauses.append(f"tags = ${param_count}")
                    values.append(value)
                else:
                    # Convert camelCase to snake_case
                    db_key = key.replace('lastUpdated', 'last_updated').replace('categoryId', 'category_id')
                    set_clauses.append(f"{db_key} = ${param_count}")
                    values.append(value)
                param_count += 1
            
            if set_clauses:
                values.append(subcategory_id)
                query = f"UPDATE subcategories SET {', '.join(set_clauses)} WHERE id = ${param_count}"
                await conn.execute(query, *values)
                return await self.get_subcategory(subcategory_id)
        return None

# Global database service instance
db_service = DatabaseService()