"""Chunk management service for document processing."""

from typing import List, Optional, Dict, Any
import asyncpg
from src.config import config
import json


class ChunkService:
    """Service for managing document chunks."""
    
    def __init__(self):
        self.db_url = config.SUPABASE_DB_URL
        self._pool = None
    
    async def get_pool(self):
        """Get or create database connection pool."""
        if self._pool is None:
            self._pool = await asyncpg.create_pool(self.db_url)
        return self._pool
    
    async def close(self):
        """Close database connection pool."""
        if self._pool:
            await self._pool.close()
    
    async def get_chunks_by_file_id(self, file_id: str) -> List[Dict[str, Any]]:
        """Get all chunks for a specific file/document ID.
        
        Args:
            file_id: The parent document ID
            
        Returns:
            List of chunk dictionaries with content, metadata, and embedding info
        """
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Query chunks by file_id (assuming we add this field)
            rows = await conn.fetch(
                """
                SELECT id, content, metadata, embedding, created_at 
                FROM document_embedding 
                WHERE file_id = $1 
                ORDER BY (metadata->>'chunk_index')::int
                """,
                file_id
            )
            
            chunks = []
            for row in rows:
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                chunks.append({
                    'id': row['id'],
                    'content': row['content'],
                    'metadata': metadata,
                    'embedding': row['embedding'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None
                })
            
            return chunks
    
    async def get_chunks_by_metadata_id(self, document_id: str) -> List[Dict[str, Any]]:
        """Get all chunks for a document using metadata->id field (current approach).
        
        Args:
            document_id: The document ID stored in metadata
            
        Returns:
            List of chunk dictionaries
        """
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Query using JSON path for metadata id
            rows = await conn.fetch(
                """
                SELECT id, content, metadata, embedding, created_at 
                FROM document_embedding 
                WHERE metadata->>'id' = $1 
                ORDER BY (metadata->>'chunk_index')::int
                """,
                document_id
            )
            
            chunks = []
            for row in rows:
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                chunks.append({
                    'id': row['id'],
                    'content': row['content'],
                    'metadata': metadata,
                    'embedding': row['embedding'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None
                })
            
            return chunks
    
    async def get_chunk_summary(self, file_id: str) -> Dict[str, Any]:
        """Get summary information about chunks for a file.
        
        Args:
            file_id: The parent document ID
            
        Returns:
            Dictionary with chunk statistics
        """
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            # Get chunk statistics
            row = await conn.fetchrow(
                """
                SELECT 
                    COUNT(*) as total_chunks,
                    AVG(LENGTH(content)) as avg_chunk_size,
                    MIN((metadata->>'chunk_index')::int) as first_chunk,
                    MAX((metadata->>'chunk_index')::int) as last_chunk,
                    metadata->>'title' as title,
                    metadata->>'source' as source,
                    metadata->>'category' as category
                FROM document_embedding 
                WHERE file_id = $1
                GROUP BY metadata->>'title', metadata->>'source', metadata->>'category'
                """,
                file_id
            )
            
            if row:
                return {
                    'file_id': file_id,
                    'total_chunks': row['total_chunks'],
                    'avg_chunk_size': int(row['avg_chunk_size']) if row['avg_chunk_size'] else 0,
                    'chunk_range': f"{row['first_chunk']}-{row['last_chunk']}",
                    'title': row['title'],
                    'source': row['source'],
                    'category': row['category']
                }
            
            return {'file_id': file_id, 'total_chunks': 0}
    
    async def search_chunks_by_content(self, query: str, file_id: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Search chunks by content similarity.
        
        Args:
            query: Search query
            file_id: Optional file ID to limit search scope
            limit: Maximum number of results
            
        Returns:
            List of matching chunks with similarity scores
        """
        # This would require embedding the query and doing vector similarity search
        # For now, we'll do a simple text search
        pool = await self.get_pool()
        async with pool.acquire() as conn:
            if file_id:
                rows = await conn.fetch(
                    """
                    SELECT id, content, metadata, created_at,
                           ts_rank(to_tsvector('english', content), plainto_tsquery('english', $1)) as rank
                    FROM document_embedding 
                    WHERE file_id = $2 
                    AND to_tsvector('english', content) @@ plainto_tsquery('english', $1)
                    ORDER BY rank DESC
                    LIMIT $3
                    """,
                    query, file_id, limit
                )
            else:
                rows = await conn.fetch(
                    """
                    SELECT id, content, metadata, created_at,
                           ts_rank(to_tsvector('english', content), plainto_tsquery('english', $1)) as rank
                    FROM document_embedding 
                    WHERE to_tsvector('english', content) @@ plainto_tsquery('english', $1)
                    ORDER BY rank DESC
                    LIMIT $2
                    """,
                    query, limit
                )
            
            chunks = []
            for row in rows:
                metadata = row['metadata']
                if isinstance(metadata, str):
                    metadata = json.loads(metadata)
                
                chunks.append({
                    'id': row['id'],
                    'content': row['content'],
                    'metadata': metadata,
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                    'relevance_score': float(row['rank']) if row['rank'] else 0.0
                })
            
            return chunks


# Global chunk service instance
chunk_service = ChunkService()