"""Pydantic models and LLM initialization."""

from pydantic import BaseModel, Field
from langchain.chat_models import init_chat_model
from langchain_ollama import OllamaEmbeddings
from .config import config


class GradeDocuments(BaseModel):
    """Grade documents using a binary score for relevance check."""

    binary_score: str = Field(
        description="Relevance score: 'yes' if relevant, or 'no' if not relevant"
    )


def get_response_model():
    """Initialize the main response model."""
    return init_chat_model(config.OPENAI_MODEL_NAME, temperature=config.LLM_TEMPERATURE)


def get_grader_model():
    """Initialize the document grader model."""
    return init_chat_model(config.OPENAI_MODEL_NAME, temperature=config.GRADER_TEMPERATURE)


def get_embeddings():
    """Initialize the embedding model."""
    return OllamaEmbeddings(model=config.OPENAI_EMB_MODEL_NAME)