"""Workflow builder for the Agentic RAG system."""

from langgraph.graph import <PERSON><PERSON>raph, START, END, MessagesState
from langgraph.prebuilt import ToolNode, tools_condition
from .workflow_nodes import WorkflowNodes


def build_workflow(retriever_tool):
    """Build and compile the Agentic RAG workflow."""
    nodes = WorkflowNodes(retriever_tool)
    
    workflow = StateGraph(MessagesState)
    
    # Register nodes
    workflow.add_node("generate_query_or_respond", nodes.generate_query_or_respond)
    workflow.add_node("retrieve", ToolNode([retriever_tool]))
    workflow.add_node("rewrite_question", nodes.rewrite_question)
    workflow.add_node("generate_answer", nodes.generate_answer)
    
    # Add edges
    workflow.add_edge(START, "generate_query_or_respond")
    workflow.add_conditional_edges(
        "generate_query_or_respond",
        tools_condition,
        {
            "tools": "retrieve",
            END: END,
        },
    )
    workflow.add_conditional_edges(
        "retrieve",
        nodes.grade_documents,
    )
    workflow.add_edge("generate_answer", END)
    workflow.add_edge("rewrite_question", "generate_query_or_respond")
    
    return workflow.compile()