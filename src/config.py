"""Configuration management for the Agentic RAG system."""

from dotenv import load_dotenv
import os

load_dotenv()

class Config:
    """Application configuration."""
    
    # OpenAI/LLM Configuration
    OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
    OPENAI_API_BASE = os.environ["OPENAI_API_BASE"]
    OPENAI_MODEL_NAME = os.environ["OPENAI_MODEL_NAME"]
    OPENAI_EMB_MODEL_NAME = os.environ["OPENAI_EMB_MODEL_NAME"]
    
    # Supabase Configuration
    SUPABASE_URL = os.environ.get("SUPABASE_URL", "http://127.0.0.1:54321")
    SUPABASE_ANON_KEY = os.environ.get("SUPABASE_ANON_KEY", "")
    SUPABASE_SERVICE_ROLE_KEY = os.environ.get("SUPABASE_SERVICE_ROLE_KEY", "")
    SUPABASE_DB_URL = os.environ.get("SUPABASE_DB_URL", "postgresql://postgres:postgres@127.0.0.1:54322/postgres")
    
    # Model Settings
    LLM_TEMPERATURE = 0.6
    GRADER_TEMPERATURE = 0
    
    # Document Processing
    CHUNK_SIZE = 100
    CHUNK_OVERLAP = 50
    
    # URLs to scrape
    # Note: Real URLs are stored in context.py
    URLS = [
        # "https://lilianweng.github.io/posts/2024-11-28-reward-hacking/",
        # "https://lilianweng.github.io/posts/2024-07-07-hallucination/",
        # "https://lilianweng.github.io/posts/2024-04-12-diffusion-video/",
        # "https://lilianweng.github.io/posts/2025-05-01-thinking/",
    ]

config = Config()