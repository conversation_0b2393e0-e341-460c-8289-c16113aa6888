"""Document upload and management router."""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any, List
import uuid
import os
from datetime import datetime
import mimetypes
from supabase import create_client, Client
from src.config import config
from src.database_service import db_service
from src.schemas.context import Document
import json

router = APIRouter(prefix="/api/documents", tags=["Document"])

# Initialize Supabase client
supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_SERVICE_ROLE_KEY)

# Default username for bucket separation
DEFAULT_USERNAME = "bao"

async def get_or_create_bucket(username: str = DEFAULT_USERNAME) -> str:
    """Get or create a storage bucket for the user."""
    # Bucket names must be lowercase and contain only letters, numbers, and hyphens
    bucket_name = f"documents-{username.lower()}"
    
    try:
        # Try to get bucket info
        bucket_info = supabase.storage.get_bucket(bucket_name)
        if bucket_info:
            return bucket_name
    except Exception:
        # Bucket doesn't exist, create it
        try:
            result = supabase.storage.create_bucket(
                bucket_name,
                options={"public": False}
            )
            return bucket_name
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to create bucket: {str(e)}")
    
    return bucket_name

def get_file_type_from_mime(mime_type: str) -> str:
    """Determine file type from MIME type."""
    if mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('video/'):
        return 'video'
    elif mime_type.startswith('audio/'):
        return 'audio'
    elif mime_type in ['application/pdf']:
        return 'pdf'
    elif mime_type in ['text/markdown', 'text/x-markdown']:
        return 'markdown'
    elif mime_type.startswith('text/'):
        return 'text'
    elif mime_type in ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed']:
        return 'archive'
    else:
        return 'other'

@router.post("/from-file")
async def upload_document_from_file(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: str = Form(""),
    subcategory_id: str = Form(...),
    username: str = Form(DEFAULT_USERNAME),
    process_vectors: bool = Form(False)  # 默认不处理向量化
):
    """Upload a document file with optional attachment storage."""
    
    try:
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Get or create user bucket
        bucket_name = await get_or_create_bucket(username)
        
        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        
        # Determine file type
        mime_type = file.content_type or mimetypes.guess_type(file.filename)[0] or 'application/octet-stream'
        file_type = get_file_type_from_mime(mime_type)
        
        # Generate storage path
        file_extension = os.path.splitext(file.filename)[1]
        storage_path = f"{document_id}/{file.filename}"
        
        # Upload file to Supabase Storage
        storage_result = supabase.storage.from_(bucket_name).upload(
            path=storage_path,
            file=file_content,
            file_options={
                "content-type": mime_type,
                "upsert": "true"
            }
        )
        
        if not storage_result:
            raise HTTPException(status_code=500, detail="Failed to upload file to storage")
        
        # Get file URL (private URL since bucket is private)
        file_url = supabase.storage.from_(bucket_name).get_public_url(storage_path)
        
        # Create document record
        now = datetime.now()
        document = Document(
            id=document_id,
            name=name,
            url=file_url,
            description=description,
            createAt=now.strftime("%Y-%m-%d %H:%M:%S"),
            lastUpdated=now.strftime("%Y-%m-%d %H:%M:%S"),
            size=f"{file_size / 1024:.1f} KB" if file_size < 1024*1024 else f"{file_size / (1024*1024):.1f} MB",
            tags=[file_type, username],
            source=file.filename,
            subcategoryId=subcategory_id,
            content="",  # File content not stored in DB for attachments
            metadata={
                "file_type": file_type,
                "mime_type": mime_type,
                "original_filename": file.filename,
                "storage_bucket": bucket_name,
                "storage_path": storage_path,
                "file_size_bytes": file_size,
                "has_attachment": True,
                "username": username
            }
        )
        
        # Save to database
        created_document = await db_service.create_document(document)
        
        # Return response
        return {
            "success": True,
            "document": {
                "id": created_document.id,
                "name": created_document.name,
                "description": created_document.description,
                "size": created_document.size,
                "url": created_document.url,
                "subcategoryId": created_document.subcategoryId,
                "lastUpdated": created_document.lastUpdated,
                "tags": created_document.tags,
                "metadata": created_document.metadata
            },
            "isVectorized": False,  # 暂时不处理向量化
            "totalChunks": 0,
            "vectorizedAt": None,
            "storage": {
                "bucket": bucket_name,
                "path": storage_path,
                "url": file_url
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload document: {str(e)}")

@router.post("/from-url")
async def upload_document_from_url(
    url: str,
    name: str,
    description: str = "",
    subcategory_id: str = "",
    username: str = DEFAULT_USERNAME
):
    """Upload a document from URL (existing functionality)."""
    
    try:
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Create document record
        now = datetime.now()
        document = Document(
            id=document_id,
            name=name,
            url=url,
            description=description,
            createAt=now.strftime("%Y-%m-%d %H:%M:%S"),
            lastUpdated=now.strftime("%Y-%m-%d %H:%M:%S"),
            size="Unknown",
            tags=["url", username],
            source=url,
            subcategoryId=subcategory_id,
            content="",  # URL content would be fetched separately
            metadata={
                "file_type": "url",
                "has_attachment": False,
                "username": username
            }
        )
        
        # Save to database
        created_document = await db_service.create_document(document)
        
        return {
            "success": True,
            "document": {
                "id": created_document.id,
                "name": created_document.name,
                "description": created_document.description,
                "size": created_document.size,
                "url": created_document.url,
                "subcategoryId": created_document.subcategoryId,
                "lastUpdated": created_document.lastUpdated,
                "tags": created_document.tags
            },
            "isVectorized": False,
            "totalChunks": 0,
            "vectorizedAt": None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload document from URL: {str(e)}")

from pydantic import BaseModel

class TextDocumentRequest(BaseModel):
    content: str
    name: str
    description: str = ""
    subcategory_id: str = ""
    username: str = DEFAULT_USERNAME

@router.post("/from-text")
async def upload_document_from_text(request: TextDocumentRequest):
    """Upload a document from text content (existing functionality)."""
    
    try:
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Create document record
        now = datetime.now()
        document = Document(
            id=document_id,
            name=request.name,
            url="",
            description=request.description,
            createAt=now.strftime("%Y-%m-%d %H:%M:%S"),
            lastUpdated=now.strftime("%Y-%m-%d %H:%M:%S"),
            size=f"{len(request.content)} chars",
            tags=["text", request.username],
            source="text_input",
            subcategoryId=request.subcategory_id,
            content=request.content,
            metadata={
                "file_type": "text",
                "has_attachment": False,
                "username": request.username,
                "content_length": len(request.content)
            }
        )
        
        # Save to database
        created_document = await db_service.create_document(document)
        
        return {
            "success": True,
            "document": {
                "id": created_document.id,
                "name": created_document.name,
                "description": created_document.description,
                "size": created_document.size,
                "subcategoryId": created_document.subcategoryId,
                "lastUpdated": created_document.lastUpdated,
                "tags": created_document.tags
            },
            "isVectorized": False,
            "totalChunks": 0,
            "vectorizedAt": None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload document from text: {str(e)}")

@router.get("/{document_id}/download")
async def download_document(document_id: str):
    """Download document attachment if it exists."""
    
    try:
        # Get document from database
        document = await db_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check if document has attachment
        metadata = document.metadata or {}
        if not metadata.get("has_attachment"):
            raise HTTPException(status_code=400, detail="Document has no attachment")
        
        # Get storage info
        bucket_name = metadata.get("storage_bucket")
        storage_path = metadata.get("storage_path")
        
        if not bucket_name or not storage_path:
            raise HTTPException(status_code=500, detail="Storage information not found")
        
        # Get signed URL for download
        signed_url = supabase.storage.from_(bucket_name).create_signed_url(
            path=storage_path,
            expires_in=3600  # 1 hour
        )
        
        return {
            "download_url": signed_url.get("signedURL"),
            "filename": metadata.get("original_filename"),
            "mime_type": metadata.get("mime_type"),
            "size": metadata.get("file_size_bytes")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get download URL: {str(e)}")

@router.delete("/{document_id}")
async def delete_document(document_id: str):
    """Delete document and its attachment if exists."""
    
    try:
        # Get document from database
        document = await db_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Delete attachment from storage if exists
        metadata = document.metadata or {}
        if metadata.get("has_attachment"):
            bucket_name = metadata.get("storage_bucket")
            storage_path = metadata.get("storage_path")
            
            if bucket_name and storage_path:
                try:
                    supabase.storage.from_(bucket_name).remove([storage_path])
                except Exception as e:
                    print(f"Warning: Failed to delete attachment: {e}")
        
        # Delete document from database
        success = await db_service.delete_document(document_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete document from database")
        
        return {"success": True, "message": "Document deleted successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")

@router.get("/buckets")
async def list_buckets():
    """List all storage buckets (for debugging)."""
    try:
        buckets = supabase.storage.list_buckets()
        return {"buckets": buckets}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list buckets: {str(e)}")