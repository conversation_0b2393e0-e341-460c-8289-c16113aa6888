"""API routes for chunk management."""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from src.document_processor import get_document_chunks, get_document_chunk_summary
from src.chunk_service import chunk_service

router = APIRouter(prefix="/chunks", tags=["chunks"])


@router.get("/document/{file_id}")
async def get_chunks_by_document(
    file_id: str,
    use_file_id: bool = Query(True, description="Use file_id column (True) or metadata->id (False)")
) -> Dict[str, Any]:
    """Get all chunks for a specific document.
    
    Args:
        file_id: The document/file ID
        use_file_id: Whether to use the optimized file_id column or legacy metadata approach
    
    Returns:
        Dictionary containing chunks and summary information
    """
    try:
        # Get chunks
        chunks = await get_document_chunks(file_id, use_file_id=use_file_id)
        
        # Get summary
        summary = await get_document_chunk_summary(file_id)
        
        return {
            "file_id": file_id,
            "summary": summary,
            "chunks": chunks,
            "total_chunks": len(chunks)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve chunks: {str(e)}")


@router.get("/document/{file_id}/summary")
async def get_document_summary(file_id: str) -> Dict[str, Any]:
    """Get summary statistics for a document's chunks.
    
    Args:
        file_id: The document/file ID
    
    Returns:
        Dictionary with chunk statistics
    """
    try:
        summary = await get_document_chunk_summary(file_id)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve summary: {str(e)}")


@router.get("/search")
async def search_chunks(
    query: str = Query(..., description="Search query"),
    file_id: Optional[str] = Query(None, description="Optional file ID to limit search scope"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results")
) -> Dict[str, Any]:
    """Search chunks by content.
    
    Args:
        query: Search query
        file_id: Optional file ID to limit search to specific document
        limit: Maximum number of results
    
    Returns:
        Dictionary containing search results
    """
    try:
        chunks = await chunk_service.search_chunks_by_content(query, file_id, limit)
        
        return {
            "query": query,
            "file_id": file_id,
            "results": chunks,
            "total_results": len(chunks)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/document/{file_id}/chunk/{chunk_index}")
async def get_specific_chunk(
    file_id: str,
    chunk_index: int,
    use_file_id: bool = Query(True, description="Use file_id column (True) or metadata->id (False)")
) -> Dict[str, Any]:
    """Get a specific chunk by its index within a document.
    
    Args:
        file_id: The document/file ID
        chunk_index: The chunk index (0-based)
        use_file_id: Whether to use the optimized file_id column
    
    Returns:
        Dictionary containing the specific chunk
    """
    try:
        chunks = await get_document_chunks(file_id, use_file_id=use_file_id)
        
        # Find chunk by index
        target_chunk = None
        for chunk in chunks:
            if chunk['metadata'].get('chunk_index') == chunk_index:
                target_chunk = chunk
                break
        
        if not target_chunk:
            raise HTTPException(
                status_code=404, 
                detail=f"Chunk with index {chunk_index} not found in document {file_id}"
            )
        
        return {
            "file_id": file_id,
            "chunk_index": chunk_index,
            "chunk": target_chunk
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve chunk: {str(e)}")