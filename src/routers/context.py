from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Optional, Literal
from datetime import datetime
import uuid
from ..schemas.context import (
    DocumentItem, DocumentCreateRequest, DocumentUpdateRequest, DocumentSearchRequest, DocumentStats,
    Category, SubCategory, Document, SubCategoryCreateRequest, SubCategoryUpdateRequest, 
    DocumentToSubCategoryRequest, DocumentFromUrlRequest
)
from ..database_service import db_service

router = APIRouter(prefix="/api", tags=["Context"])

def _convert_document_to_item(doc: Document, subcategory: SubCategory, category: Category) -> DocumentItem:
    """将Document转换为DocumentItem的辅助函数"""
    # 处理metadata，确保包含url信息和向量化状态
    metadata = doc.metadata or {}
    if doc.url:
        # 如果有url，添加到metadata中以保持兼容性
        if 'urls' not in metadata:
            metadata['urls'] = []
        if doc.url not in metadata['urls']:
            metadata['urls'].insert(0, doc.url)  # 主URL放在第一位
    
    # 从metadata中提取向量化状态
    is_vectorized = metadata.get('isVectorized', False)
    vectorized_at = metadata.get('vectorizedAt', None)
        
    # 创建DocumentItem
    doc_item = DocumentItem(
        id=doc.id,
        name=doc.name,
        type=subcategory.type,
        category=category.name,
        subCategory=subcategory.name,
        subCategoryId=subcategory.id,  # 新增
        priority=subcategory.priority,
        status=subcategory.status,
        lastUpdated=doc.lastUpdated,
        size=doc.size,
        compression=subcategory.compression,
        relevanceScore=subcategory.relevanceScore,
        tags=doc.tags,
        description=doc.description or "",
        source=doc.source,
        knowledgeGraph=subcategory.knowledgeGraph,
        url=doc.url,
        metadata=metadata,
        isVectorized=is_vectorized,
        vectorizedAt=vectorized_at
    )
    
    return doc_item

@router.get("/documents", response_model=List[DocumentItem])
async def get_documents(
    type: Optional[Literal['business', 'personal', 'all']] = 'all',
    category: Optional[str] = None,
    status: Optional[Literal['active', 'inactive', 'processing']] = None,
    priority: Optional[Literal['high', 'medium', 'low']] = None,
    search: Optional[str] = None,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0
):
    """获取文档列表，支持多种过滤条件"""
    try:
        # 获取所有分类和子分类
        categories = await db_service.get_all_categories()
        category_map = {cat.id: cat for cat in categories}
        
        document_items = []
        
        # 遍历所有分类
        for cat in categories:
            # 应用分类过滤
            if type != 'all' and cat.type != type:
                continue
            if category and cat.name != category:
                continue
            
            # 获取该分类下的子分类
            subcategories = await db_service.get_subcategories_by_category(cat.id)
            
            for subcat in subcategories:
                # 应用子分类过滤
                if status and subcat.status != status:
                    continue
                if priority and subcat.priority != priority:
                    continue
                    
                # 获取该子分类下的文档
                documents = await db_service.get_documents_by_subcategory(subcat.id)
                
                for doc in documents:
                    # 应用搜索过滤
                    if search:
                        search_lower = search.lower()
                        if not (search_lower in doc.name.lower() or
                               search_lower in (doc.description or "").lower() or
                               any(search_lower in tag.lower() for tag in doc.tags)):
                            continue
                    
                    doc_item = _convert_document_to_item(doc, subcat, cat)
                    document_items.append(doc_item)
        
        # 分页
        document_items = document_items[offset:offset + limit]
        return document_items
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get documents: {str(e)}")

@router.get("/documents/{document_id}", response_model=DocumentItem)
async def get_document(document_id: str):
    """获取单个文档详情"""
    try:
        document = await db_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        subcategory = await db_service.get_subcategory(document.subcategoryId)
        if not subcategory:
            raise HTTPException(status_code=500, detail="SubCategory not found")
            
        category = await db_service.get_category(subcategory.categoryId)
        if not category:
            raise HTTPException(status_code=500, detail="Category not found")
        
        doc_item = _convert_document_to_item(document, subcategory, category)
        return doc_item
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get document: {str(e)}")

@router.post("/documents", response_model=Document)
async def create_document(request: DocumentCreateRequest):
    """创建新文档"""
    try:
        # 验证子类别是否存在
        categories = await db_service.get_all_categories()
        subcategory = None
        
        for cat in categories:
            if cat.name == request.category:
                subcategories = await db_service.get_subcategories_by_category(cat.id)
                subcategory = next((sc for sc in subcategories if sc.name == request.subCategory), None)
                break
        
        if not subcategory:
            raise HTTPException(status_code=400, detail="SubCategory not found")
        
        new_doc = Document(
            id=f"doc_{str(uuid.uuid4())[:8]}",
            name=request.name,
            url=request.url,
            description=request.description,
            createAt=datetime.now().strftime("%Y-%m-%d"),
            lastUpdated=datetime.now().strftime("%Y-%m-%d"),
            size="0 KB",
            tags=request.tags,
            source=request.source,
            subcategoryId=subcategory.id,
            content=request.content,
            metadata={}
        )
        
        created_doc = await db_service.create_document(new_doc)
        return created_doc
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create document: {str(e)}")

@router.put("/documents/{document_id}", response_model=Document)
async def update_document(document_id: str, request: DocumentUpdateRequest):
    """更新文档信息"""
    try:
        document = await db_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        update_data = request.dict(exclude_unset=True)
        
        # 如果更新子类别，需要验证并转换为subcategoryId
        if 'subCategory' in update_data:
            categories = await db_service.get_all_categories()
            subcategory = None
            
            for cat in categories:
                subcategories = await db_service.get_subcategories_by_category(cat.id)
                subcategory = next((sc for sc in subcategories if sc.name == update_data['subCategory']), None)
                if subcategory:
                    break
            
            if not subcategory:
                raise HTTPException(status_code=400, detail="SubCategory not found")
            update_data['subcategoryId'] = subcategory.id
            del update_data['subCategory']
        
        updated_doc = await db_service.update_document(document_id, update_data)
        return updated_doc
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update document: {str(e)}")

@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """删除文档"""
    try:
        success = await db_service.delete_document(document_id)
        if not success:
            raise HTTPException(status_code=404, detail="Document not found")
        return {"message": "Document deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")

@router.get("/documents/stats", response_model=DocumentStats)
async def get_document_stats():
    """获取文档统计信息"""
    try:
        categories = await db_service.get_all_categories()
        
        business_count = 0
        personal_count = 0
        knowledge_graph_count = 0
        total_relevance = 0
        valid_docs = 0
        
        for cat in categories:
            subcategories = await db_service.get_subcategories_by_category(cat.id)
            
            for subcat in subcategories:
                documents = await db_service.get_documents_by_subcategory(subcat.id)
                
                for doc in documents:
                    if subcat.type == 'business':
                        business_count += 1
                    elif subcat.type == 'personal':
                        personal_count += 1
                    
                    if subcat.knowledgeGraph:
                        knowledge_graph_count += 1
                    
                    total_relevance += subcat.relevanceScore
                    valid_docs += 1
        
        avg_relevance = total_relevance / valid_docs if valid_docs > 0 else 0
        
        return DocumentStats(
            businessDocuments=business_count,
            personalKnowledge=personal_count,
            knowledgeGraphNodes=knowledge_graph_count,
            averageRelevance=int(avg_relevance * 100)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get document stats: {str(e)}")

@router.get("/categories", response_model=List[Category])
async def get_categories():
    """获取所有大类别"""
    try:
        return await db_service.get_all_categories()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")

@router.get("/subcategories", response_model=List[SubCategory])
async def get_subcategories(category: Optional[str] = None):
    """获取子类别，可按大类别过滤"""
    try:
        if category:
            # 找到对应的大类别ID
            categories = await db_service.get_all_categories()
            category_obj = next((cat for cat in categories if cat.name == category), None)
            if category_obj:
                subcategories = await db_service.get_subcategories_by_category(category_obj.id)
            else:
                subcategories = []
        else:
            # 获取所有子类别
            categories = await db_service.get_all_categories()
            subcategories = []
            for cat in categories:
                cat_subcategories = await db_service.get_subcategories_by_category(cat.id)
                subcategories.extend(cat_subcategories)
        
        return subcategories
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get subcategories: {str(e)}")

@router.post("/documents/search", response_model=List[DocumentItem])
async def search_documents(request: DocumentSearchRequest):
    """搜索文档"""
    return await get_documents(
        type=request.type,
        category=request.category,
        priority=request.priority,
        status=request.status,
        search=request.query,
        limit=request.limit,
        offset=0
    )

# 文件上传相关接口
@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    subcategory_id: str = Form(...),
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    tags: Optional[str] = Form(None)  # JSON字符串格式
):
    """上传文件并创建文档"""
    try:
        # 验证子类别是否存在
        subcategory = await db_service.get_subcategory(subcategory_id)
        if not subcategory:
            raise HTTPException(status_code=404, detail="SubCategory not found")
        
        # 验证文件类型
        allowed_types = ['text/plain', 'application/pdf', 'text/markdown', 'application/msword', 
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file.content_type}")
        
        # 读取文件内容
        content = await file.read()
        
        # 处理不同文件类型
        if file.content_type == 'text/plain' or file.content_type == 'text/markdown':
            text_content = content.decode('utf-8')
        elif file.content_type == 'application/pdf':
            # 处理PDF文件
            try:
                import PyPDF2
                import io
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
                text_content = ""
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
            except ImportError:
                raise HTTPException(status_code=500, detail="PDF processing not available. Please install PyPDF2.")
        else:
            # 对于其他文件类型，暂时只保存文件信息
            text_content = f"File: {file.filename}\nType: {file.content_type}\nSize: {len(content)} bytes"
        
        # 解析tags
        tag_list = []
        if tags:
            try:
                import json
                tag_list = json.loads(tags)
            except:
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
        
        # 创建文档
        document_id = str(uuid.uuid4())
        new_document = Document(
            id=document_id,
            name=name or file.filename,
            description=description or f"Uploaded file: {file.filename}",
            createAt=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            lastUpdated=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            size=f"{len(content) // 1024} KB",
            tags=tag_list,
            source="file_upload",
            subcategoryId=subcategory_id,
            content=text_content,
            metadata={
                'originalFilename': file.filename,
                'contentType': file.content_type,
                'fileSize': len(content),
                'uploadedAt': datetime.now().isoformat()
            }
        )
        
        created_doc = await db_service.create_document(new_document)
        
        return {
            "message": "File uploaded and document created successfully",
            "document": created_doc,
            "filename": file.filename,
            "size": len(content)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

@router.post("/documents/{document_id}/vectorize")
async def vectorize_document(document_id: str):
    """向量化指定文档"""
    try:
        document = await db_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # 检查是否已经向量化
        metadata = document.metadata or {}
        if metadata.get('isVectorized', False):
            return {"message": "Document is already vectorized", "isVectorized": True}
        
        # 导入必要的模块
        from ..models import get_embeddings
        from ..supabase_vectorstore import SupabaseVectorStore
        from langchain_core.documents import Document as LangchainDocument
        
        # 获取嵌入模型
        embeddings = get_embeddings()
        
        # 创建向量存储实例
        vectorstore = SupabaseVectorStore(
            embedding=embeddings,
            table_name="document_embedding"
        )
        
        # 获取子类别和类别信息
        subcategory = await db_service.get_subcategory(document.subcategoryId)
        category = None
        if subcategory:
            category = await db_service.get_category(subcategory.categoryId)
        
        # 创建 Langchain Document 对象
        langchain_doc = LangchainDocument(
            page_content=document.content or "",
            metadata={
                "id": document.id,
                "title": document.name,
                "category": category.name if category else "unknown",
                "subcategory": subcategory.name if subcategory else "unknown",
                "source": "database",
                "vectorized_at": datetime.now().isoformat()
            }
        )
        
        # 向量化并存储到数据库
        doc_ids = vectorstore.add_texts(
            texts=[document.content or ""],
            metadatas=[langchain_doc.metadata],
            ids=[document.id]
        )
        
        # 更新文档的向量化状态
        update_metadata = document.metadata or {}
        update_metadata['isVectorized'] = True
        update_metadata['vectorizedAt'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        await db_service.update_document(document_id, {'metadata': update_metadata})
        
        return {
            "message": "Document vectorized and stored successfully",
            "isVectorized": True,
            "vectorizedAt": update_metadata['vectorizedAt'],
            "stored_ids": doc_ids
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Vectorization failed: {str(e)}")

@router.post("/embedding/document/from-url")
async def create_document_from_url(request: DocumentFromUrlRequest):
    """从 URL 创建并向量化文档"""
    try:
        # 验证 URL 格式
        if not request.url or not request.url.startswith(('http://', 'https://')):
            raise HTTPException(status_code=400, detail="Invalid URL format")
        
        # 验证子类别是否存在
        subcategory = await db_service.get_subcategory(request.subcategory_id)
        if not subcategory:
            raise HTTPException(status_code=404, detail=f"Subcategory not found with id: {request.subcategory_id}")
        
        # 导入必要的模块
        from ..document_processor import load_and_split_documents
        from ..models import get_embeddings
        from ..supabase_vectorstore import SupabaseVectorStore
        from langchain_core.documents import Document as LangchainDocument
        
        print(f"开始从 URL 加载文档: {request.url}")
        
        # 从 URL 加载和分割文档
        doc_splits = load_and_split_documents([request.url])
        
        if not doc_splits:
            raise HTTPException(status_code=400, detail="Failed to load document from URL")
        
        print(f"文档加载完成，共获得 {len(doc_splits)} 个文档块")
        
        # 获取嵌入模型
        embeddings = get_embeddings()
        
        # 创建向量存储实例
        vectorstore = SupabaseVectorStore(
            embedding=embeddings,
            table_name="document_embedding"
        )
        
        # 获取类别信息
        category = await db_service.get_category(subcategory.categoryId)
        file_id = subcategory.categoryId  # 直接用 categoryId 作为 file_id
        
        # 生成文档 ID
        document_id = str(uuid.uuid4())
        
        # 从第一个文档块获取基本信息
        first_doc = doc_splits[0]
        doc_title = request.name or first_doc.metadata.get('title', f"Document from {request.url}")
        doc_content = "\n\n".join([doc.page_content for doc in doc_splits])
        
        # 准备向量化的文档
        texts = [doc.page_content for doc in doc_splits]
        metadatas = []
        doc_ids = []
        
        for i, doc in enumerate(doc_splits):
            chunk_id = f"{document_id}_chunk_{i}"
            metadata = {
                "id": document_id,
                "chunk_id": chunk_id,
                "title": doc_title,
                "category": category.name if category else "unknown",
                "subcategory": subcategory.name,
                "source": request.url,
                "source_type": "url",
                "vectorized_at": datetime.now().isoformat(),
                "chunk_index": i,
                "total_chunks": len(doc_splits),
                "file_id": file_id  # 新增 file_id 字段
            }
            # 保留原始文档的其他元数据
            if hasattr(doc, 'metadata') and doc.metadata:
                metadata.update({k: v for k, v in doc.metadata.items() if k not in metadata})
            
            metadatas.append(metadata)
            doc_ids.append(chunk_id)
        
        # 向量化并存储到数据库
        stored_ids = vectorstore.add_texts(
            texts=texts,
            metadatas=metadatas,
            ids=doc_ids
        )
        
        # 创建新的文档记录
        new_document = Document(
            id=document_id,
            name=doc_title,
            content=doc_content,
            url=request.url,
            subcategoryId=request.subcategory_id,
            description=request.description or f"Document loaded from {request.url}",
            createAt=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            lastUpdated=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            size=f"{len(doc_content) // 1024} KB",
            tags=[],
            source="url",
            metadata={
                'isVectorized': True,
                'vectorizedAt': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'sourceUrl': request.url,
                'sourceType': 'url',
                'totalChunks': len(doc_splits),
                'urls': [request.url]
            }
        )
        
        # 保存到数据库
        created_doc = await db_service.create_document(new_document)
        
        print(f"文档创建并向量化完成: {doc_title}")
        
        return {
            "message": "Document created and vectorized successfully",
            "document": created_doc,
            "isVectorized": True,
            "vectorizedAt": new_document.metadata['vectorizedAt'],
            "totalChunks": len(doc_splits),
            "stored_ids": stored_ids
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to create document from URL: {str(e)}")

# 子类别相关接口
@router.get("/subcategories/{subcategory_id}", response_model=SubCategory)
async def get_subcategory(subcategory_id: str):
    """获取特定子类别的详细信息"""
    try:
        subcategory = await db_service.get_subcategory(subcategory_id)
        if not subcategory:
            raise HTTPException(status_code=404, detail="SubCategory not found")
        return subcategory
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get subcategory: {str(e)}")

@router.post("/subcategories", response_model=SubCategory)
async def create_subcategory(request: SubCategoryCreateRequest):
    """创建新的子类别"""
    try:
        # 验证大类别是否存在
        categories = await db_service.get_all_categories()
        category = next((cat for cat in categories if cat.name == request.category), None)
        if not category:
            raise HTTPException(status_code=400, detail="Category not found")
        
        new_subcategory = SubCategory(
            id=f"sc_{str(uuid.uuid4())[:8]}",
            name=request.name,
            type=request.type,
            categoryId=category.id,
            priority=request.priority,
            status="active",
            lastUpdated=datetime.now().strftime("%Y-%m-%d"),
            size="0 KB",
            compression=0,
            relevanceScore=0.0,
            tags=request.tags,
            description=request.description,
            source=request.source,
            knowledgeGraph=False,
            metadata={}
        )
        
        created_subcategory = await db_service.create_subcategory(new_subcategory)
        return created_subcategory
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create subcategory: {str(e)}")

@router.get("/subcategories/{subcategory_id}/documents", response_model=List[Document])
async def get_documents_in_subcategory(
    subcategory_id: str,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0
):
    """获取子类别下的所有文档"""
    try:
        subcategory = await db_service.get_subcategory(subcategory_id)
        if not subcategory:
            raise HTTPException(status_code=404, detail="SubCategory not found")
        
        documents = await db_service.get_documents_by_subcategory(subcategory_id)
        documents = documents[offset:offset + limit]
        return documents
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get documents in subcategory: {str(e)}")

@router.post("/subcategories/{subcategory_id}/documents", response_model=Document)
async def add_document_to_subcategory(subcategory_id: str, request: DocumentToSubCategoryRequest):
    """向子类别添加新文档"""
    try:
        subcategory = await db_service.get_subcategory(subcategory_id)
        if not subcategory:
            raise HTTPException(status_code=404, detail="SubCategory not found")
        
        new_document = Document(
            id=f"doc_{subcategory_id}_{str(uuid.uuid4())[:8]}",
            name=request.name,
            url=request.url,
            description=request.description,
            createAt=datetime.now().strftime("%Y-%m-%d"),
            lastUpdated=datetime.now().strftime("%Y-%m-%d"),
            size="0 KB",
            tags=request.tags,
            source=request.source,
            subcategoryId=subcategory_id,
            content=request.content,
            metadata={}
        )
        
        created_doc = await db_service.create_document(new_document)
        
        # 更新子类别的统计信息
        documents = await db_service.get_documents_by_subcategory(subcategory_id)
        total_size = len(documents)
        await db_service.update_subcategory(subcategory_id, {
            "size": f"{total_size * 0.5:.1f} MB",
            "lastUpdated": datetime.now().strftime("%Y-%m-%d")
        })
        
        return created_doc
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add document to subcategory: {str(e)}")
