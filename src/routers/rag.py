from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from src.rag_service import get_rag_service

router = APIRouter(prefix="/api/rag", tags=["RAG"])

@router.get("/health")
async def rag_health():
    return {"status": "rag ok"}

@router.get("/query")
async def rag_query(question: str) -> Dict[str, Any]:
    service = get_rag_service()
    result = service.query_sync(question)
    return result

@router.post("/reload")
async def rag_reload(urls: list[str] = None):
    service = get_rag_service()
    service.reload_documents(urls)
    return {"message": "RAG documents reloaded"} 