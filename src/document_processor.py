"""Document loading and processing utilities."""

from langchain_community.document_loaders import WebBaseLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.vectorstores import InMemoryVectorStore
from langchain.tools.retriever import create_retriever_tool
from .config import config
from .models import get_embeddings
from .supabase_vectorstore import SupabaseVectorStore
from .chunk_service import chunk_service


def get_urls_from_context():
    """Extract URLs from context documents."""
    # Return real URLs for document loading
    real_urls = [
        "https://lilianweng.github.io/posts/2024-11-28-reward-hacking/",
        "https://lilianweng.github.io/posts/2024-07-07-hallucination/",
        "https://blog.langchain.com/context-engineering-for-agents/",
        "https://cloud.google.com/discover/what-is-agentic-ai"
    ]
    return real_urls


def load_and_split_documents(urls: list[str] = None, chunk_size: int = None, chunk_overlap: int = None):
    """Load documents from URLs and split them into chunks."""
    urls = urls or get_urls_from_context()
    chunk_size = chunk_size or config.CHUNK_SIZE
    chunk_overlap = chunk_overlap or config.CHUNK_OVERLAP
    
    print(f"\n=== 开始加载文档 ===")
    print(f"文档URL列表 ({len(urls)} 个):")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
    
    docs = [WebBaseLoader(url).load() for url in urls]
    docs_list = [item for sublist in docs for item in sublist]
    
    print(f"\n=== 文档加载完成 ===")
    print(f"总共加载了 {len(docs_list)} 个文档:")
    for i, doc in enumerate(docs_list, 1):
        title = getattr(doc.metadata, 'title', '未知标题') if hasattr(doc, 'metadata') and doc.metadata else '未知标题'
        content_length = len(doc.page_content) if hasattr(doc, 'page_content') else 0
        print(f"  {i}. 标题: {title}")
        print(f"     内容长度: {content_length} 字符")
        print(f"     来源: {doc.metadata.get('source', '未知来源') if hasattr(doc, 'metadata') and doc.metadata else '未知来源'}")
        print(f"     预览: {doc.page_content[:100] if hasattr(doc, 'page_content') and doc.page_content else '无内容'}...")
        print()
    
    text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        chunk_size=chunk_size, chunk_overlap=chunk_overlap
    )
    doc_splits = text_splitter.split_documents(docs_list)
    
    print(f"=== 文档分割完成 ===")
    print(f"分割后共有 {len(doc_splits)} 个文档块")
    print(f"平均每个文档块长度: {sum(len(split.page_content) for split in doc_splits) // len(doc_splits) if doc_splits else 0} 字符\n")
    
    return doc_splits


def create_vector_store_and_retriever(doc_splits=None, use_supabase=True, collection_name="documents"):
    """Create vector store and retriever from document splits.
    
    Args:
        doc_splits: Pre-split documents. If None, will load and split documents.
        use_supabase: Whether to use Supabase vector store (True) or in-memory store (False).
        collection_name: Name of the Supabase collection (only used when use_supabase=True).
    
    Returns:
        Tuple of (vectorstore, retriever, retriever_tool)
    """
    if doc_splits is None:
        doc_splits = load_and_split_documents()
    
    embeddings = get_embeddings()
    
    print(f"\n=== 创建向量存储 ===")
    if use_supabase:
        print(f"使用 Supabase 向量存储，集合名称: {collection_name}")
        try:
            vectorstore = SupabaseVectorStore.from_documents(
                documents=doc_splits, 
                embedding=embeddings,
                table_name=collection_name
            )
            print(f"成功创建 Supabase 向量存储，存储了 {len(doc_splits)} 个文档块")
        except Exception as e:
            print(f"创建 Supabase 向量存储失败: {e}")
            print("回退到内存向量存储")
            vectorstore = InMemoryVectorStore.from_documents(
                documents=doc_splits, embedding=embeddings
            )
    else:
        print("使用内存向量存储")
        vectorstore = InMemoryVectorStore.from_documents(
            documents=doc_splits, embedding=embeddings
        )
    
    retriever = vectorstore.as_retriever()
    
    retriever_tool = create_retriever_tool(
        retriever,
        "retrieve_blog_posts",
        "Search and return information about Lilian Weng blog posts.",
    )
    
    print("向量存储和检索器创建完成\n")
    return vectorstore, retriever, retriever_tool


async def get_document_chunks(file_id: str, use_file_id: bool = True):
    """Get all chunks for a specific document.
    
    Args:
        file_id: The document/file ID
        use_file_id: If True, use the new file_id field; if False, use metadata->id
    
    Returns:
        List of document chunks with metadata
    """
    if use_file_id:
        # Use the new optimized approach with file_id column
        chunks = await chunk_service.get_chunks_by_file_id(file_id)
    else:
        # Use the legacy approach with metadata->id
        chunks = await chunk_service.get_chunks_by_metadata_id(file_id)
    
    return chunks


async def get_document_chunk_summary(file_id: str):
    """Get summary statistics for a document's chunks.
    
    Args:
        file_id: The document/file ID
    
    Returns:
        Dictionary with chunk statistics
    """
    return await chunk_service.get_chunk_summary(file_id)