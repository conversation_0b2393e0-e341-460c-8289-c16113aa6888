from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime

class Category(BaseModel):
    """Top-level category containing multiple subcategories."""
    id: str
    name: str  # 大类别名称，如 "网络资源"
    type: Literal['business', 'personal']
    priority: Literal['high', 'medium', 'low']
    status: Literal['active', 'inactive', 'processing']
    lastUpdated: str
    description: str
    tags: List[str]
    metadata: Optional[Dict[str, Any]] = None

class Document(BaseModel):
    """Individual document within a subcategory."""
    id: str
    name: str
    url: Optional[str] = None
    description: Optional[str] = None
    createAt: str
    lastUpdated: str
    size: str
    tags: List[str] = []
    source: str
    subcategoryId: str  # 所属子类别ID
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class SubCategory(BaseModel):
    """Sub-category containing multiple documents."""
    id: str
    name: str  # 子类别名称，如 "技术博客"
    type: Literal['business', 'personal']
    categoryId: str  # 所属大类别ID
    priority: Literal['high', 'medium', 'low']
    status: Literal['active', 'inactive', 'processing']
    lastUpdated: str
    size: str  # 整个子类别的总大小
    compression: int = Field(ge=0, le=100)
    relevanceScore: float = Field(ge=0.0, le=1.0)
    tags: List[str]
    description: str
    source: str
    knowledgeGraph: Optional[bool] = False
    metadata: Optional[Dict[str, Any]] = None

# 为了保持向后兼容，保留原有的 DocumentItem
class DocumentItem(BaseModel):
    """Document item model matching frontend interface (legacy)."""
    id: str
    name: str
    type: Literal['business', 'personal']
    category: str
    subCategory: str
    subCategoryId: str  # 🆕 新增
    priority: Literal['high', 'medium', 'low']
    status: Literal['active', 'inactive', 'processing']
    lastUpdated: str
    size: str
    compression: int = Field(ge=0, le=100)
    relevanceScore: float = Field(ge=0.0, le=1.0)
    tags: List[str]
    description: str
    source: str
    knowledgeGraph: Optional[bool] = False
    url: Optional[str] = None  # 🆕 添加url字段
    metadata: Optional[Dict[str, Any]] = None
    # 🆕 添加向量化状态字段
    isVectorized: Optional[bool] = False
    vectorizedAt: Optional[str] = None

class DocumentCreateRequest(BaseModel):
    """Request model for creating documents."""
    name: str
    type: Literal['business', 'personal']
    category: str
    subCategory: str
    priority: Literal['high', 'medium', 'low'] = 'medium'
    description: str
    source: str
    tags: List[str] = []
    content: Optional[str] = None
    url: Optional[str] = None

class DocumentUpdateRequest(BaseModel):
    """Request model for updating documents."""
    name: Optional[str] = None
    category: Optional[str] = None
    subCategory: Optional[str] = None
    priority: Optional[Literal['high', 'medium', 'low']] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    status: Optional[Literal['active', 'inactive', 'processing']] = None

class DocumentSearchRequest(BaseModel):
    """Request model for document search."""
    query: str
    type: Optional[Literal['business', 'personal', 'all']] = 'all'
    category: Optional[str] = None
    priority: Optional[Literal['high', 'medium', 'low']] = None
    status: Optional[Literal['active', 'inactive', 'processing']] = None
    limit: Optional[int] = 50

class DocumentStats(BaseModel):
    """Document statistics model."""
    businessDocuments: int
    personalKnowledge: int
    knowledgeGraphNodes: int
    averageRelevance: int

class SubCategoryCreateRequest(BaseModel):
    """Request model for creating subcategories."""
    name: str
    type: Literal['business', 'personal']
    category: str
    priority: Literal['high', 'medium', 'low'] = 'medium'
    description: str
    source: str
    tags: List[str] = []

class SubCategoryUpdateRequest(BaseModel):
    """Request model for updating subcategories."""
    name: Optional[str] = None
    category: Optional[str] = None
    priority: Optional[Literal['high', 'medium', 'low']] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    status: Optional[Literal['active', 'inactive', 'processing']] = None

class DocumentToSubCategoryRequest(BaseModel):
    """Request model for adding documents to subcategories."""
    name: str
    url: Optional[str] = None
    description: Optional[str] = None
    tags: List[str] = []
    source: str
    content: Optional[str] = None

class DocumentFromUrlRequest(BaseModel):
    """Request model for creating documents from URL."""
    url: str
    subcategory_id: str
    name: Optional[str] = None
    description: Optional[str] = None
