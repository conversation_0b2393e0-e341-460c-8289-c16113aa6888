"""Supabase vector store implementation for document storage and retrieval."""

import logging
import uuid
import json
from typing import List, Optional, Dict, Any
from supabase import create_client, Client
import psycopg2
from psycopg2.extras import RealDictCursor
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_core.vectorstores import VectorStore
from .config import config

logger = logging.getLogger(__name__)


class SupabaseVectorStore(VectorStore):
    """Supabase-based vector store implementation using pgvector."""
    
    def __init__(
        self,
        embedding: Embeddings,
        table_name: str = "document_embedding"
    ):
        """Initialize Supabase vector store.
        
        Args:
            embedding: Embedding function to use
            table_name: Name of the database table
        """
        self.embedding = embedding
        self.table_name = table_name
        
        # Initialize Supabase client
        self.supabase: Client = create_client(
            config.SUPABASE_URL,
            config.SUPABASE_SERVICE_ROLE_KEY
        )
        
        # Test connection and ensure table exists
        self._ensure_table()
    
    def _get_connection(self):
        """Get a direct PostgreSQL connection."""
        return psycopg2.connect(config.SUPABASE_DB_URL)
    
    def _ensure_table(self):
        """Ensure the document_embedding table exists with proper schema."""
        try:
            conn = self._get_connection()
            with conn.cursor() as cur:
                # Check if pgvector extension is enabled
                cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
                
                # Get embedding dimension
                test_embedding = self.embedding.embed_query("test")
                dimension = len(test_embedding)
                
                # Check if table exists and has correct dimension
                cur.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = %s AND column_name = 'embedding';
                """, (self.table_name,))
                
                existing_column = cur.fetchone()
                table_needs_recreation = False
                
                if existing_column:
                    # Extract dimension from the vector type (e.g., "vector(1024)" -> 1024)
                    if existing_column[1] == 'USER-DEFINED':  # This is a vector type
                        # Get the actual vector dimension
                        cur.execute(f"""
                        SELECT atttypmod 
                        FROM pg_attribute 
                        WHERE attrelid = '{self.table_name}'::regclass 
                        AND attname = 'embedding';
                        """)
                        typmod_result = cur.fetchone()
                        if typmod_result and typmod_result[0] != dimension:
                            logger.info(f"Table '{self.table_name}' exists but has wrong dimension. Recreating...")
                            table_needs_recreation = True
                
                if table_needs_recreation:
                    # Drop the existing table
                    cur.execute(f"DROP TABLE IF EXISTS {self.table_name} CASCADE;")
                    logger.info(f"Dropped existing table '{self.table_name}' due to dimension mismatch")
                
                # Create table if it doesn't exist or was just dropped
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    metadata JSONB DEFAULT '{{}}',
                    embedding VECTOR({dimension}),
                    file_id TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """
                cur.execute(create_table_sql)
                
                # Create indexes
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS {self.table_name}_embedding_idx 
                ON {self.table_name} USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100);
                """)
                
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS {self.table_name}_metadata_idx 
                ON {self.table_name} USING GIN (metadata);
                """)
                
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS {self.table_name}_file_id_idx 
                ON {self.table_name} (file_id);
                """)
                
                conn.commit()
                logger.info(f"Table '{self.table_name}' ensured with dimension {dimension}")
                
        except Exception as e:
            logger.error(f"Error ensuring table: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_texts(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> List[str]:
        """Add texts to the vector store.
        
        Args:
            texts: List of texts to add
            metadatas: Optional list of metadata dicts
            ids: Optional list of IDs
            
        Returns:
            List of IDs of added documents
        """
        if not texts:
            return []
        
        # Generate embeddings
        embeddings = self.embedding.embed_documents(texts)
        
        # Prepare data for insertion
        records = []
        generated_ids = []
        
        for i, (text, embedding) in enumerate(zip(texts, embeddings)):
            record_id = ids[i] if ids and i < len(ids) else str(uuid.uuid4())
            metadata = metadatas[i] if metadatas and i < len(metadatas) else {}
            
            # Extract file_id from metadata if available
            file_id = metadata.get('id') if metadata else None
            
            records.append((record_id, text, json.dumps(metadata), embedding, file_id))
            generated_ids.append(record_id)
        
        # Insert records
        conn = self._get_connection()
        try:
            with conn.cursor() as cur:
                insert_sql = f"""
                INSERT INTO {self.table_name} (id, content, metadata, embedding, file_id)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    content = EXCLUDED.content,
                    metadata = EXCLUDED.metadata,
                    embedding = EXCLUDED.embedding,
                    file_id = EXCLUDED.file_id;
                """
                cur.executemany(insert_sql, records)
                conn.commit()
                
            logger.info(f"Added {len(records)} documents to table '{self.table_name}'")
            return generated_ids
            
        except Exception as e:
            logger.error(f"Error adding texts: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    def similarity_search(
        self,
        query: str,
        k: int = 4,
        **kwargs: Any,
    ) -> List[Document]:
        """Search for similar documents.
        
        Args:
            query: Query string
            k: Number of results to return
            
        Returns:
            List of similar documents
        """
        results = self.similarity_search_with_score(query, k, **kwargs)
        return [doc for doc, _ in results]
    
    def similarity_search_with_score(
        self,
        query: str,
        k: int = 4,
        **kwargs: Any,
    ) -> List[tuple[Document, float]]:
        """Search for similar documents with similarity scores.
        
        Args:
            query: Query string
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        # Generate query embedding
        query_embedding = self.embedding.embed_query(query)
        
        conn = self._get_connection()
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Use cosine similarity for search
                # Convert embedding to string format for PostgreSQL
                embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'
                
                search_sql = f"""
                SELECT id, content, metadata, 
                       1 - (embedding <=> %s::vector) as similarity
                FROM {self.table_name}
                WHERE embedding IS NOT NULL
                ORDER BY embedding <=> %s::vector
                LIMIT %s;
                """
                
                logger.info(f"Executing search on table '{self.table_name}' with query: '{query}'")
                logger.info(f"Embedding dimension: {len(query_embedding)}")
                
                # First check if there are any documents with embeddings
                cur.execute(f"SELECT COUNT(*) as count FROM {self.table_name} WHERE embedding IS NOT NULL;")
                embed_count = cur.fetchone()['count']
                logger.info(f"Documents with embeddings in table: {embed_count}")
                
                if embed_count > 0:
                    # Show a sample document for debugging
                    cur.execute(f"SELECT id, LEFT(content, 30) as preview FROM {self.table_name} LIMIT 1;")
                    sample = cur.fetchone()
                    logger.info(f"Sample document: {sample['id']} - {sample['preview']}")
                
                try:
                    cur.execute(search_sql, (embedding_str, embedding_str, k))
                    results = cur.fetchall()
                    logger.info(f"Search returned {len(results)} results")
                except Exception as search_error:
                    logger.error(f"Search SQL failed: {search_error}")
                    logger.error(f"SQL: {search_sql}")
                    logger.error(f"Parameters: embedding_str length={len(embedding_str)}, k={k}")
                    # Try a simpler query
                    logger.info("Trying simpler query...")
                    cur.execute(f"SELECT COUNT(*) FROM {self.table_name};")
                    total = cur.fetchone()['count']
                    logger.info(f"Total rows in table: {total}")
                    results = []
                
                # Convert results to Document objects with scores
                documents_with_scores = []
                for result in results:
                    doc = Document(
                        page_content=result['content'],
                        metadata=result['metadata'] if result['metadata'] else {}
                    )
                    similarity = float(result['similarity'])
                    documents_with_scores.append((doc, similarity))
                
                return documents_with_scores
                
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            import traceback
            traceback.print_exc()
            raise
        finally:
            if conn:
                conn.close()
    
    def delete(self, ids: Optional[List[str]] = None, **kwargs: Any) -> Optional[bool]:
        """Delete documents by IDs.
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            True if successful
        """
        if not ids:
            return False
        
        conn = self._get_connection()
        try:
            with conn.cursor() as cur:
                delete_sql = f"DELETE FROM {self.table_name} WHERE id = ANY(%s);"
                cur.execute(delete_sql, (ids,))
                deleted_count = cur.rowcount
                conn.commit()
                
            logger.info(f"Deleted {deleted_count} documents from table '{self.table_name}'")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting documents: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def clear(self) -> None:
        """Clear all documents from the table."""
        conn = self._get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"DELETE FROM {self.table_name};")
                deleted_count = cur.rowcount
                conn.commit()
                
            logger.info(f"Cleared {deleted_count} documents from table '{self.table_name}'")
            
        except Exception as e:
            logger.error(f"Error clearing table: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def get_document_count(self) -> int:
        """Get the total number of documents in the store."""
        conn = self._get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"SELECT COUNT(*) FROM {self.table_name};")
                count = cur.fetchone()[0]
                return count
        except Exception as e:
            logger.error(f"Error getting document count: {e}")
            return 0
        finally:
            conn.close()
    
    @classmethod
    def from_texts(
        cls,
        texts: List[str],
        embedding: Embeddings,
        metadatas: Optional[List[Dict[str, Any]]] = None,
        table_name: str = "document_embedding",
        **kwargs: Any,
    ) -> "SupabaseVectorStore":
        """Create a vector store from texts.
        
        Args:
            texts: List of texts
            embedding: Embedding function
            metadatas: Optional metadata
            table_name: Name of the table
            
        Returns:
            SupabaseVectorStore instance
        """
        store = cls(
            embedding=embedding,
            table_name=table_name,
            **kwargs
        )
        store.add_texts(texts, metadatas)
        return store
    
    @classmethod
    def from_documents(
        cls,
        documents: List[Document],
        embedding: Embeddings,
        table_name: str = "document_embedding",
        **kwargs: Any,
    ) -> "SupabaseVectorStore":
        """Create a vector store from documents.
        
        Args:
            documents: List of Document objects
            embedding: Embedding function
            table_name: Name of the table
            
        Returns:
            SupabaseVectorStore instance
        """
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        
        return cls.from_texts(
            texts=texts,
            embedding=embedding,
            metadatas=metadatas,
            table_name=table_name,
            **kwargs
        )