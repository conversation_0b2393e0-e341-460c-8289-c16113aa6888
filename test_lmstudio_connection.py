#!/usr/bin/env python3
"""
测试 LM Studio 连接和模型可用性
"""

import os
import requests
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI, OpenAIEmbeddings

load_dotenv()

# LM Studio 配置
lm_studio_api_key = os.environ.get("LM_STUDIO_API_KEY", "lm-studio")
lm_studio_base_url = os.environ.get("LM_STUDIO_BASE_URL", "http://localhost:1234/v1")
lm_studio_chat_model = os.environ.get("LM_STUDIO_CHAT_MODEL", "qwen3-4b-thinking-2507-mlx")
lm_studio_embed_model = os.environ.get("LM_STUDIO_EMBED_MODEL", "snowflake-arctic-embed-l-v2.0")

def test_lm_studio_api():
    """测试 LM Studio API 是否可用"""
    try:
        # 测试基本连接
        response = requests.get(f"{lm_studio_base_url.rstrip('/v1')}/health", timeout=5)
        if response.status_code == 200:
            print("✅ LM Studio API 连接成功")
            return True
        else:
            print(f"❌ LM Studio API 连接失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ LM Studio API 连接失败: {e}")
        return False

def test_models_list():
    """获取可用模型列表"""
    try:
        response = requests.get(f"{lm_studio_base_url}/models", 
                              headers={"Authorization": f"Bearer {lm_studio_api_key}"}, 
                              timeout=10)
        if response.status_code == 200:
            models = response.json()
            print("✅ 可用模型列表:")
            for model in models.get("data", []):
                print(f"  - {model.get('id', 'Unknown')}")
            return models
        else:
            print(f"❌ 获取模型列表失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取模型列表失败: {e}")
        return None

def test_chat_model():
    """测试聊天模型"""
    try:
        print(f"🧪 测试聊天模型: {lm_studio_chat_model}")
        chat_model = ChatOpenAI(
            model=lm_studio_chat_model,
            base_url=lm_studio_base_url,
            api_key=lm_studio_api_key,
            temperature=0.1
        )
        
        response = chat_model.invoke([{"role": "user", "content": "Hello! Please respond with 'LM Studio is working!'"}])
        print(f"✅ 聊天模型响应: {response.content}")
        return True
    except Exception as e:
        print(f"❌ 聊天模型测试失败: {e}")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    try:
        print(f"🧪 测试嵌入模型: {lm_studio_embed_model}")
        embedding_model = OpenAIEmbeddings(
            model=lm_studio_embed_model,
            openai_api_base=lm_studio_base_url,
            openai_api_key=lm_studio_api_key
        )
        
        # 测试嵌入
        test_text = "This is a test sentence for embedding."
        embeddings = embedding_model.embed_query(test_text)
        print(f"✅ 嵌入模型工作正常，向量维度: {len(embeddings)}")
        return True
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 LM Studio 连接...")
    print(f"📍 Base URL: {lm_studio_base_url}")
    print(f"💬 聊天模型: {lm_studio_chat_model}")
    print(f"🔤 嵌入模型: {lm_studio_embed_model}")
    print("-" * 50)
    
    # 测试 API 连接
    if not test_lm_studio_api():
        print("\n❌ LM Studio 服务未运行或配置错误")
        print("请确保:")
        print("1. LM Studio 已启动")
        print("2. 本地服务器已开启 (通常在端口 1234)")
        print("3. 模型已加载")
        return False
    
    # 获取模型列表
    models = test_models_list()
    
    # 测试聊天模型
    chat_success = test_chat_model()
    
    # 测试嵌入模型
    embed_success = test_embedding_model()
    
    print("-" * 50)
    if chat_success and embed_success:
        print("🎉 所有测试通过！LM Studio 配置正确。")
        return True
    else:
        print("⚠️  部分测试失败，请检查模型配置。")
        return False

if __name__ == "__main__":
    main()
