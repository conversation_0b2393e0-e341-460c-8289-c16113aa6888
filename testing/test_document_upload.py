#!/usr/bin/env python3
"""
文档上传功能测试脚本

测试附件上传功能，包括：
1. 文件上传
2. URL上传
3. 文本上传
4. 文件下载
5. 文档删除
6. 存储桶管理
"""

import requests
import json
import os
import tempfile
from typing import Dict, Any

API_BASE = "http://localhost:8000/api/documents"
TEST_USERNAME = "bao"

def create_test_file(content: str, filename: str) -> str:
    """创建测试文件"""
    temp_dir = tempfile.gettempdir()
    file_path = os.path.join(temp_dir, filename)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return file_path

def test_file_upload():
    """测试文件上传功能"""
    print("🧪 测试文件上传功能...")
    
    # 创建测试文件
    test_content = """这是一个测试文档，用于验证附件上传功能。

内容包括：
1. 中文文本支持
2. 多行文本处理
3. 特殊字符：@#$%^&*()

测试完成后可以删除此文件。"""
    
    test_file_path = create_test_file(test_content, "test_upload_api.txt")
    
    try:
        # 准备上传数据
        files = {'file': open(test_file_path, 'rb')}
        data = {
            'name': '测试文档API',
            'description': '通过API测试上传的文档',
            'subcategory_id': 'sc_business_design',
            'username': TEST_USERNAME
        }
        
        # 发送请求
        response = requests.post(f"{API_BASE}/from-file", files=files, data=data)
        files['file'].close()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文件上传成功!")
            print(f"   文档ID: {result['document']['id']}")
            print(f"   存储路径: {result['storage']['path']}")
            print(f"   文件大小: {result['document']['size']}")
            return result['document']['id']
        else:
            print(f"❌ 文件上传失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 文件上传异常: {str(e)}")
        return None
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_url_upload():
    """测试URL上传功能"""
    print("\n🧪 测试URL上传功能...")
    
    data = {
        'url': 'https://example.com',
        'name': '示例网站API测试',
        'description': '通过API测试URL上传',
        'subcategory_id': 'sc_personal_blogs',
        'username': TEST_USERNAME
    }
    
    try:
        response = requests.post(f"{API_BASE}/from-url", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ URL上传成功!")
            print(f"   文档ID: {result['document']['id']}")
            print(f"   URL: {result['document']['url']}")
            return result['document']['id']
        else:
            print(f"❌ URL上传失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ URL上传异常: {str(e)}")
        return None

def test_text_upload():
    """测试文本上传功能"""
    print("\n🧪 测试文本上传功能...")
    
    data = {
        'content': '''这是通过API上传的文本内容。

包含多段文字：

第一段：介绍文档的用途和背景
第二段：详细说明功能特性
第三段：总结和后续计划

特殊字符测试：!@#$%^&*()_+-=[]{}|;:,.<>?

中文字符测试：你好世界！这是一个测试文档。''',
        'name': '文本文档API测试',
        'description': '通过API测试文本上传功能',
        'subcategory_id': 'sc_personal_ai',
        'username': TEST_USERNAME
    }
    
    try:
        response = requests.post(f"{API_BASE}/from-text", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文本上传成功!")
            print(f"   文档ID: {result['document']['id']}")
            print(f"   内容长度: {result['document']['size']}")
            return result['document']['id']
        else:
            print(f"❌ 文本上传失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 文本上传异常: {str(e)}")
        return None

def test_document_download(document_id: str):
    """测试文档下载功能"""
    print(f"\n🧪 测试文档下载功能 (ID: {document_id})...")
    
    try:
        response = requests.get(f"{API_BASE}/{document_id}/download")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取下载链接成功!")
            print(f"   文件名: {result.get('filename', 'N/A')}")
            print(f"   MIME类型: {result.get('mime_type', 'N/A')}")
            print(f"   文件大小: {result.get('size', 'N/A')} bytes")
            print(f"   下载链接: {result.get('download_url', 'N/A')[:50]}...")
            return True
        else:
            print(f"❌ 获取下载链接失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 下载测试异常: {str(e)}")
        return False

def test_document_delete(document_id: str):
    """测试文档删除功能"""
    print(f"\n🧪 测试文档删除功能 (ID: {document_id})...")
    
    try:
        response = requests.delete(f"{API_BASE}/{document_id}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档删除成功!")
            print(f"   消息: {result.get('message', 'N/A')}")
            return True
        else:
            print(f"❌ 文档删除失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 删除测试异常: {str(e)}")
        return False

def test_list_buckets():
    """测试存储桶列表功能"""
    print("\n🧪 测试存储桶列表功能...")
    
    try:
        response = requests.get(f"{API_BASE}/buckets")
        
        if response.status_code == 200:
            result = response.json()
            buckets = result.get('buckets', [])
            print(f"✅ 获取存储桶列表成功! 共 {len(buckets)} 个存储桶")
            
            for bucket in buckets:
                print(f"   - {bucket.get('name', 'N/A')} (公开: {bucket.get('public', False)})")
            
            return True
        else:
            print(f"❌ 获取存储桶列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 存储桶列表测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始文档上传功能测试")
    print("=" * 50)
    
    # 存储文档ID用于后续测试
    document_ids = []
    
    # 1. 测试存储桶列表
    test_list_buckets()
    
    # 2. 测试文件上传
    file_doc_id = test_file_upload()
    if file_doc_id:
        document_ids.append(file_doc_id)
    
    # 3. 测试URL上传
    url_doc_id = test_url_upload()
    if url_doc_id:
        document_ids.append(url_doc_id)
    
    # 4. 测试文本上传
    text_doc_id = test_text_upload()
    if text_doc_id:
        document_ids.append(text_doc_id)
    
    # 5. 测试下载功能（仅对有附件的文档）
    if file_doc_id:
        test_document_download(file_doc_id)
    
    # 6. 测试删除功能
    print(f"\n🧪 清理测试数据...")
    for doc_id in document_ids:
        test_document_delete(doc_id)
    
    print("\n" + "=" * 50)
    print("🎉 文档上传功能测试完成!")

if __name__ == "__main__":
    main()