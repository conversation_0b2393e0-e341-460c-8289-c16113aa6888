#!/usr/bin/env python3
"""Test script for Supabase vector store functionality."""

import sys
import os
import asyncio
from typing import List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import create_vector_store_and_retriever, load_and_split_documents
from src.models import get_embeddings
from src.supabase_vectorstore import SupabaseVectorStore
from langchain_core.documents import Document


def test_basic_functionality():
    """Test basic Supabase vector store functionality."""
    print("=== 测试基本功能 ===")
    
    try:
        # Test embedding function
        print("1. 测试嵌入模型...")
        embeddings = get_embeddings()
        test_embedding = embeddings.embed_query("测试文本")
        print(f"   嵌入维度: {len(test_embedding)}")
        print("   ✓ 嵌入模型正常工作")
        
        # Test vector store creation
        print("\n2. 测试向量存储创建...")
        test_docs = [
            Document(page_content="这是第一个测试文档", metadata={"source": "test1"}),
            Document(page_content="这是第二个测试文档", metadata={"source": "test2"}),
            Document(page_content="这是关于机器学习的文档", metadata={"source": "test3"}),
        ]
        
        vectorstore = SupabaseVectorStore.from_documents(
            documents=test_docs,
            embedding=embeddings,
            table_name="test_collection"
        )
        print("   ✓ 向量存储创建成功")
        
        # Test similarity search
        print("\n3. 测试相似性搜索...")
        results = vectorstore.similarity_search("机器学习", k=2)
        print(f"   搜索结果数量: {len(results)}")
        for i, doc in enumerate(results):
            print(f"   结果 {i+1}: {doc.page_content[:50]}...")
        print("   ✓ 相似性搜索正常工作")
        
        # Test search with scores
        print("\n4. 测试带分数的搜索...")
        results_with_scores = vectorstore.similarity_search_with_score("测试", k=3)
        print(f"   搜索结果数量: {len(results_with_scores)}")
        for i, (doc, score) in enumerate(results_with_scores):
            print(f"   结果 {i+1} (分数: {score:.4f}): {doc.page_content[:30]}...")
        print("   ✓ 带分数搜索正常工作")
        
        # Clean up test collection
        print("\n5. 清理测试数据...")
        vectorstore.clear()
        print("   ✓ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_document_processor_integration():
    """Test integration with document processor."""
    print("\n=== 测试文档处理器集成 ===")
    
    try:
        # Create test documents
        print("1. 创建测试文档...")
        test_docs = [
            Document(
                page_content="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
                metadata={"source": "ai_intro", "title": "人工智能简介"}
            ),
            Document(
                page_content="机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
                metadata={"source": "ml_intro", "title": "机器学习简介"}
            ),
            Document(
                page_content="深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。",
                metadata={"source": "dl_intro", "title": "深度学习简介"}
            ),
        ]
        
        # Test with Supabase
        print("\n2. 测试 Supabase 向量存储...")
        vectorstore, retriever, retriever_tool = create_vector_store_and_retriever(
            doc_splits=test_docs,
            use_supabase=True,
            collection_name="test_integration"
        )
        
        # Test retriever
        print("\n3. 测试检索器...")
        query = "什么是机器学习？"
        retrieved_docs = retriever.invoke(query)
        print(f"   查询: {query}")
        print(f"   检索到 {len(retrieved_docs)} 个文档:")
        for i, doc in enumerate(retrieved_docs):
            print(f"   文档 {i+1}: {doc.page_content[:50]}...")
        
        # Test retriever tool
        print("\n4. 测试检索工具...")
        tool_result = retriever_tool.invoke({"query": "深度学习"})
        print(f"   工具查询结果: {tool_result[:100]}...")
        
        # Clean up
        print("\n5. 清理测试数据...")
        if hasattr(vectorstore, 'clear'):
            vectorstore.clear()
        print("   ✓ 集成测试完成")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fallback_to_memory():
    """Test fallback to in-memory store when Supabase fails."""
    print("\n=== 测试回退到内存存储 ===")
    
    try:
        # Test with invalid Supabase config (should fallback to memory)
        print("1. 测试内存存储回退...")
        test_docs = [
            Document(page_content="测试文档1", metadata={"source": "test1"}),
            Document(page_content="测试文档2", metadata={"source": "test2"}),
        ]
        
        # Force use of memory store
        vectorstore, retriever, retriever_tool = create_vector_store_and_retriever(
            doc_splits=test_docs,
            use_supabase=False
        )
        
        # Test that it works
        results = retriever.invoke("测试")
        print(f"   内存存储检索结果: {len(results)} 个文档")
        print("   ✓ 内存存储回退正常工作")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 回退测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("开始测试 Supabase 向量存储功能...\n")
    
    # Check if Supabase is running
    print("=== 检查 Supabase 连接 ===")
    try:
        from src.config import config
        print(f"Supabase URL: {config.SUPABASE_URL}")
        print(f"数据库 URL: {config.SUPABASE_DB_URL}")
        print("✓ 配置加载成功")
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False
    
    # Run tests
    tests = [
        ("基本功能测试", test_basic_functionality),
        ("文档处理器集成测试", test_document_processor_integration),
        ("内存存储回退测试", test_fallback_to_memory),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 个测试通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)