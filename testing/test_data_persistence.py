#!/usr/bin/env python3
"""Test data persistence in Supabase vector store."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.supabase_vectorstore import SupabaseVectorStore
from src.models import get_embeddings
from langchain_core.documents import Document


def test_data_persistence():
    """Test that data persists correctly in Supabase."""
    print("=== 测试数据持久化 ===\n")
    
    # Create test documents
    test_docs = [
        Document(
            page_content="Python是一种高级编程语言，以其简洁和可读性而闻名。",
            metadata={"language": "python", "topic": "programming", "level": "beginner"}
        ),
        Document(
            page_content="机器学习是人工智能的一个分支，专注于让计算机从数据中学习。",
            metadata={"language": "chinese", "topic": "machine_learning", "level": "intermediate"}
        ),
        Document(
            page_content="Supabase是一个开源的Firebase替代品，提供数据库、认证和实时功能。",
            metadata={"language": "chinese", "topic": "database", "level": "advanced"}
        ),
    ]
    
    try:
        # Step 1: Create vector store and add documents
        print("1. 创建向量存储并添加文档...")
        embeddings = get_embeddings()
        vectorstore = SupabaseVectorStore.from_documents(
            documents=test_docs,
            embedding=embeddings,
            table_name="test_persistence"
        )
        
        initial_count = vectorstore.get_document_count()
        print(f"   添加后文档总数: {initial_count}")
        
        # Step 2: Test search
        print("\n2. 测试搜索功能...")
        search_results = vectorstore.similarity_search("编程语言", k=2)
        print(f"   搜索 '编程语言' 找到 {len(search_results)} 个结果:")
        for i, doc in enumerate(search_results, 1):
            print(f"     {i}. {doc.page_content[:30]}...")
            print(f"        主题: {doc.metadata.get('topic', 'unknown')}")
        
        # Step 3: Test search with scores
        print("\n3. 测试带分数的搜索...")
        scored_results = vectorstore.similarity_search_with_score("机器学习", k=3)
        print(f"   搜索 '机器学习' 的结果:")
        for i, (doc, score) in enumerate(scored_results, 1):
            print(f"     {i}. 分数: {score:.4f}")
            print(f"        内容: {doc.page_content[:40]}...")
            print(f"        级别: {doc.metadata.get('level', 'unknown')}")
        
        # Step 4: Create a new instance to test persistence
        print("\n4. 创建新实例测试数据持久化...")
        new_vectorstore = SupabaseVectorStore(
            embedding=embeddings,
            table_name="test_persistence"
        )
        
        persistent_count = new_vectorstore.get_document_count()
        print(f"   新实例中的文档数: {persistent_count}")
        
        if persistent_count == initial_count:
            print("   ✓ 数据持久化成功！")
        else:
            print("   ✗ 数据持久化失败！")
            return False
        
        # Step 5: Test search with new instance
        print("\n5. 用新实例测试搜索...")
        persistent_results = new_vectorstore.similarity_search("Supabase", k=3)
        print(f"   搜索结果数量: {len(persistent_results)}")
        if persistent_results:
            print(f"   ✓ 新实例搜索成功: {persistent_results[0].page_content[:50]}...")
        else:
            # Try a more general search
            general_results = new_vectorstore.similarity_search("数据库", k=3)
            print(f"   尝试搜索 '数据库': {len(general_results)} 个结果")
            if general_results:
                print(f"   ✓ 通用搜索成功: {general_results[0].page_content[:50]}...")
            else:
                print("   ✗ 新实例搜索失败！")
                return False
        
        # Step 6: Test adding more documents
        print("\n6. 测试添加更多文档...")
        additional_docs = [
            Document(
                page_content="Docker是一个容器化平台，可以简化应用程序的部署和管理。",
                metadata={"language": "chinese", "topic": "devops", "level": "intermediate"}
            )
        ]
        
        new_vectorstore.add_texts(
            texts=[doc.page_content for doc in additional_docs],
            metadatas=[doc.metadata for doc in additional_docs]
        )
        
        final_count = new_vectorstore.get_document_count()
        print(f"   添加后总文档数: {final_count}")
        
        if final_count == initial_count + len(additional_docs):
            print("   ✓ 增量添加成功！")
        else:
            print("   ✗ 增量添加失败！")
            return False
        
        # Step 7: Test metadata filtering (if supported)
        print("\n7. 测试元数据查询...")
        all_results = new_vectorstore.similarity_search("技术", k=10)
        print(f"   找到 {len(all_results)} 个包含技术相关内容的文档")
        
        topics = {}
        for doc in all_results:
            topic = doc.metadata.get('topic', 'unknown')
            topics[topic] = topics.get(topic, 0) + 1
        
        print("   按主题分布:")
        for topic, count in topics.items():
            print(f"     - {topic}: {count} 个文档")
        
        # Step 8: Clean up (optional)
        print("\n8. 清理测试数据...")
        new_vectorstore.clear()
        cleanup_count = new_vectorstore.get_document_count()
        
        if cleanup_count == 0:
            print("   ✓ 数据清理成功！")
        else:
            print(f"   ⚠ 清理后仍有 {cleanup_count} 个文档")
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("开始测试 Supabase 向量存储的数据持久化功能...\n")
    
    success = test_data_persistence()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 数据持久化测试全部通过！")
        print("\n✅ Supabase 向量存储已经可以正常使用了！")
        print("\n主要功能:")
        print("- ✓ 文档存储和检索")
        print("- ✓ 向量相似性搜索")
        print("- ✓ 元数据支持")
        print("- ✓ 数据持久化")
        print("- ✓ 增量添加")
        print("- ✓ 数据清理")
    else:
        print("❌ 数据持久化测试失败！")
    print('='*50)
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)