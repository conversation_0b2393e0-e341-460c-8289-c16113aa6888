#!/usr/bin/env python3
"""Example usage of Supabase vector store with document processing."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import create_vector_store_and_retriever, load_and_split_documents
from langchain_core.documents import Document


def main():
    """Demonstrate Supabase vector store usage."""
    print("=== Supabase 向量存储使用示例 ===\n")
    
    # Option 1: Use with custom documents
    print("1. 使用自定义文档创建向量存储...")
    custom_docs = [
        Document(
            page_content="LangChain是一个用于构建基于大语言模型应用程序的框架。它提供了模块化的组件，使开发者能够轻松地创建复杂的AI应用。",
            metadata={"source": "langchain_intro", "category": "framework"}
        ),
        Document(
            page_content="RAG（检索增强生成）是一种结合了信息检索和文本生成的技术。它首先从知识库中检索相关信息，然后使用这些信息来生成更准确的回答。",
            metadata={"source": "rag_explanation", "category": "technique"}
        ),
        Document(
            page_content="向量数据库是专门用于存储和查询高维向量的数据库。它们通过计算向量之间的相似性来实现语义搜索功能。",
            metadata={"source": "vector_db_intro", "category": "database"}
        ),
    ]
    
    # Create vector store with Supabase
    vectorstore, retriever, retriever_tool = create_vector_store_and_retriever(
        doc_splits=custom_docs,
        use_supabase=True,
        collection_name="example_docs"
    )
    
    # Test queries
    test_queries = [
        "什么是LangChain？",
        "RAG技术是如何工作的？",
        "向量数据库有什么用途？",
        "如何构建AI应用？"
    ]
    
    print("\n2. 测试查询...")
    for i, query in enumerate(test_queries, 1):
        print(f"\n查询 {i}: {query}")
        results = retriever.invoke(query)
        print(f"找到 {len(results)} 个相关文档:")
        
        for j, doc in enumerate(results[:2], 1):  # Show top 2 results
            print(f"  结果 {j}:")
            print(f"    内容: {doc.page_content[:80]}...")
            print(f"    来源: {doc.metadata.get('source', 'unknown')}")
            print(f"    类别: {doc.metadata.get('category', 'unknown')}")
    
    # Test retriever tool
    print(f"\n3. 测试检索工具...")
    tool_query = "框架和技术"
    tool_result = retriever_tool.invoke({"query": tool_query})
    print(f"工具查询: {tool_query}")
    print(f"工具结果: {tool_result[:200]}...")
    
    # Option 2: Load documents from URLs (if available)
    print(f"\n4. 从URL加载文档（如果配置了URL）...")
    try:
        # This will use URLs from config or mock data
        url_docs = load_and_split_documents()
        if url_docs:
            print(f"从URL加载了 {len(url_docs)} 个文档块")
            
            # Create another vector store with URL documents
            url_vectorstore, url_retriever, url_retriever_tool = create_vector_store_and_retriever(
                doc_splits=url_docs[:5],  # Use first 5 chunks for demo
                use_supabase=True,
                collection_name="url_docs"
            )
            
            # Test with URL documents
            url_query = "机器学习"
            url_results = url_retriever.invoke(url_query)
            print(f"URL文档查询结果: {len(url_results)} 个文档")
        else:
            print("没有配置URL或无法加载URL文档")
    except Exception as e:
        print(f"URL文档加载失败: {e}")
    
    # Demonstrate comparison with in-memory store
    print(f"\n5. 对比内存存储...")
    memory_vectorstore, memory_retriever, memory_retriever_tool = create_vector_store_and_retriever(
        doc_splits=custom_docs,
        use_supabase=False  # Use in-memory store
    )
    
    comparison_query = "什么是RAG？"
    supabase_results = retriever.invoke(comparison_query)
    memory_results = memory_retriever.invoke(comparison_query)
    
    print(f"查询: {comparison_query}")
    print(f"Supabase 结果数量: {len(supabase_results)}")
    print(f"内存存储结果数量: {len(memory_results)}")
    
    # Clean up (optional)
    print(f"\n6. 清理测试数据...")
    try:
        if hasattr(vectorstore, 'clear'):
            vectorstore.clear()
            print("✓ Supabase 测试数据已清理")
    except Exception as e:
        print(f"清理数据时出错: {e}")
    
    print(f"\n=== 示例完成 ===")
    print("现在你可以在自己的应用中使用 Supabase 向量存储了！")
    print("\n使用方法:")
    print("1. 确保 Supabase 服务正在运行")
    print("2. 调用 create_vector_store_and_retriever(use_supabase=True)")
    print("3. 使用返回的 retriever 进行文档检索")


if __name__ == "__main__":
    main()