#!/usr/bin/env python3
"""
测试子类别映射功能

验证前端组件中的子类别映射是否正确对应到数据库中的子类别ID
"""

import requests
import json

API_BASE = "http://localhost:8000/api/documents"
SUBCATEGORIES_API = "http://localhost:8000/api/subcategories"

# 前端组件中的映射表（与前端保持一致）
SUBCATEGORY_MAPPING = {
    # 业务类别映射
    '研发设计': 'sc_business_design',
    '生产管控': 'sc_business_production',
    '运维数据': 'sc_business_operations',
    '企业标准': 'sc_business_tech',
    '其他文档': 'sc_business_design',

    # 个人类别映射  
    '学术论文': 'sc_personal_ai',
    '网络资源': 'sc_personal_blogs',
    '个人笔记': 'sc_personal_ai',
    '其他资料': 'sc_personal_blogs'
}

def get_available_subcategories():
    """获取数据库中可用的子类别"""
    try:
        response = requests.get(SUBCATEGORIES_API)
        if response.status_code == 200:
            subcategories = response.json()
            return {sub['id']: sub['name'] for sub in subcategories}
        else:
            print(f"❌ 获取子类别失败: {response.text}")
            return {}
    except Exception as e:
        print(f"❌ 获取子类别异常: {str(e)}")
        return {}

def test_subcategory_mapping():
    """测试子类别映射"""
    print("🧪 测试子类别映射...")
    
    # 获取数据库中的子类别
    db_subcategories = get_available_subcategories()
    print(f"数据库中的子类别: {list(db_subcategories.keys())}")
    
    # 检查映射表中的每个映射
    mapping_errors = []
    for chinese_name, subcategory_id in SUBCATEGORY_MAPPING.items():
        if subcategory_id not in db_subcategories:
            mapping_errors.append(f"'{chinese_name}' -> '{subcategory_id}' (不存在)")
        else:
            print(f"✅ '{chinese_name}' -> '{subcategory_id}' ({db_subcategories[subcategory_id]})")
    
    if mapping_errors:
        print("\n❌ 映射错误:")
        for error in mapping_errors:
            print(f"   {error}")
        return False
    else:
        print("\n✅ 所有子类别映射正确!")
        return True

def test_text_upload_with_each_category():
    """测试每个子类别的文本上传"""
    print("\n🧪 测试每个子类别的文本上传...")
    
    success_count = 0
    total_count = len(SUBCATEGORY_MAPPING)
    uploaded_docs = []
    
    for chinese_name, subcategory_id in SUBCATEGORY_MAPPING.items():
        print(f"\n测试 '{chinese_name}' ({subcategory_id})...")
        
        data = {
            'content': f'这是测试{chinese_name}类别的文档内容。\n\n包含一些示例文本用于验证上传功能。',
            'name': f'测试{chinese_name}文档',
            'description': f'用于测试{chinese_name}子类别的文档上传功能',
            'subcategory_id': subcategory_id,
            'username': 'bao'
        }
        
        try:
            response = requests.post(f"{API_BASE}/from-text", json=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 上传成功! 文档ID: {result['document']['id']}")
                uploaded_docs.append(result['document']['id'])
                success_count += 1
            else:
                print(f"   ❌ 上传失败: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 上传异常: {str(e)}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个子类别测试成功")
    
    # 清理测试数据
    if uploaded_docs:
        print(f"\n🧹 清理 {len(uploaded_docs)} 个测试文档...")
        for doc_id in uploaded_docs:
            try:
                response = requests.delete(f"{API_BASE}/{doc_id}")
                if response.status_code == 200:
                    print(f"   ✅ 删除文档 {doc_id}")
                else:
                    print(f"   ❌ 删除文档 {doc_id} 失败")
            except Exception as e:
                print(f"   ❌ 删除文档 {doc_id} 异常: {str(e)}")
    
    return success_count == total_count

def main():
    """主测试函数"""
    print("🚀 开始子类别映射测试")
    print("=" * 50)
    
    # 1. 测试映射表的正确性
    mapping_ok = test_subcategory_mapping()
    
    if mapping_ok:
        # 2. 测试每个子类别的文档上传
        upload_ok = test_text_upload_with_each_category()
        
        if upload_ok:
            print("\n🎉 所有测试通过!")
        else:
            print("\n⚠️  部分上传测试失败")
    else:
        print("\n❌ 子类别映射存在问题，跳过上传测试")
    
    print("=" * 50)

if __name__ == "__main__":
    main()