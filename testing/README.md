# 测试文件说明

本目录包含项目的各种测试文件和测试工具。

## 文档上传功能测试

### 测试脚本
- `test_document_upload.py` - 文档上传功能的完整API测试脚本
- `test_subcategory_mapping.py` - 子类别映射测试脚本
- `test_frontend.html` - 前端文档上传功能测试页面

### 测试文件
- `test_upload.txt` - 基础文本文件测试
- `test_image.txt` - 较大文本文件测试

### 使用方法

#### 1. API测试脚本
```bash
# 确保后端服务已启动
cd testing
python test_document_upload.py
```

#### 2. 前端测试页面
```bash
# 在浏览器中打开
open testing/test_frontend.html
# 或者
python -m http.server 8080
# 然后访问 http://localhost:8080/testing/test_frontend.html
```

## 其他测试文件

### 数据库测试
- `test_database_service.py` - 数据库服务测试
- `test_data_persistence.py` - 数据持久化测试
- `test_direct_sql.py` - 直接SQL查询测试

### RAG系统测试
- `test_rag_initialization.py` - RAG系统初始化测试
- `test_reload_endpoint.py` - 重载端点测试
- `test_supabase_vectorstore.py` - Supabase向量存储测试

### 其他工具
- `debug_search.py` - 搜索调试工具
- `example_supabase_usage.py` - Supabase使用示例
- `import_mock_data.py` - 模拟数据导入工具
- `simple_test.py` - 简单测试脚本
- `verify_supabase_data.py` - Supabase数据验证工具

## 测试环境要求

1. **后端服务**: 确保后端API服务在 `http://localhost:8000` 运行
2. **Supabase**: 确保Supabase服务正常运行
3. **Python依赖**: 安装 `requests` 库用于API测试
4. **浏览器**: 用于前端测试页面

## 测试数据清理

测试脚本会自动清理创建的测试数据，包括：
- 删除上传的测试文档
- 清理临时文件
- 移除测试存储桶中的文件

如需手动清理，可以使用：
```bash
curl -X GET "http://localhost:8000/api/documents/buckets"
curl -X DELETE "http://localhost:8000/api/documents/{document_id}"
```