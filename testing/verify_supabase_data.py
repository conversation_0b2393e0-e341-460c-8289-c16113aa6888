#!/usr/bin/env python3
"""Verify that documents are properly stored in Supabase."""

import sys
import os
# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.supabase_vectorstore import SupabaseVectorStore
from src.models import get_embeddings

def verify_supabase_data():
    """Verify that documents are stored in Supabase."""
    print("=== Verifying Supabase Data Persistence ===")
    
    try:
        # Create embeddings and vector store
        embeddings = get_embeddings()
        vectorstore = SupabaseVectorStore(
            embedding=embeddings,
            table_name="documents"
        )
        
        # Check document count
        doc_count = vectorstore.get_document_count()
        print(f"✓ Total documents in Supabase: {doc_count}")
        
        if doc_count > 0:
            # Test search functionality
            print("\nTesting search queries:")
            
            queries = [
                "reward hacking",
                "context engineering",
                "artificial intelligence",
                "language models"
            ]
            
            for query in queries:
                results = vectorstore.similarity_search(query, k=3)
                print(f"  Query: '{query}' -> {len(results)} results")
                
                if results:
                    # Show first result preview
                    first_result = results[0]
                    preview = first_result.page_content[:100].replace('\n', ' ')
                    source = first_result.metadata.get('source', 'Unknown')
                    print(f"    Top result: {preview}...")
                    print(f"    Source: {source}")
                print()
            
            print("✅ Supabase data verification completed successfully!")
            print(f"✅ {doc_count} documents are properly stored and searchable.")
            return True
            
        else:
            print("❌ No documents found in Supabase")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying Supabase data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_supabase_data()
    if not success:
        sys.exit(1)