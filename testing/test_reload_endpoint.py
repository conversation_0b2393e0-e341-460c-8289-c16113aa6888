#!/usr/bin/env python3
"""Test the reload endpoint for the RAG service."""

import sys
import os
# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import requests
import json
import time

def test_reload_endpoint():
    """Test the reload endpoint with a new URL."""
    print("=== Testing RAG Service Reload Endpoint ===")
    
    # URL for the reload endpoint
    reload_url = "http://localhost:8000/api/rag/reload"
    
    # New URLs to load
    new_urls = [
        "https://blog.langchain.com/benchmarking-multi-agent-architectures/"
    ]
    
    try:
        # Make the request to reload documents
        print(f"Sending request to reload with URL: {new_urls[0]}")
        response = requests.post(
            reload_url,
            json=new_urls,
            headers={"Content-Type": "application/json"}
        )
        
        # Check response
        if response.status_code == 200:
            print(f"✅ Reload successful: {response.json()}")
            
            # Wait a moment for processing to complete
            print("Waiting for processing to complete...")
            time.sleep(2)
            
            # Verify the new documents are searchable
            print("\nVerifying new documents are searchable...")
            query_url = "http://localhost:8000/api/rag/query"
            query_response = requests.get(
                f"{query_url}?question=multi-agent+architectures"
            )
            
            if query_response.status_code == 200:
                result = query_response.json()
                print(f"✅ Query successful")
                print(f"Response: {json.dumps(result, indent=2)[:500]}...")
                return True
            else:
                print(f"❌ Query failed: {query_response.status_code}")
                print(f"Error: {query_response.text}")
                return False
        else:
            print(f"❌ Reload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing reload endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_reload_endpoint()
    if not success:
        sys.exit(1)