#!/usr/bin/env python3
"""Simple test to demonstrate Supabase vector store usage."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import create_vector_store_and_retriever
from langchain_core.documents import Document


def main():
    """Simple demonstration of Supabase vector store."""
    print("=== 简单的 Supabase 向量存储演示 ===\n")
    
    # Create some sample documents
    sample_docs = [
        Document(
            page_content="LangChain是一个强大的框架，用于构建基于大语言模型的应用程序。它提供了丰富的工具和组件。",
            metadata={"source": "langchain_guide", "type": "framework"}
        ),
        Document(
            page_content="RAG（检索增强生成）结合了信息检索和文本生成，能够提供更准确和相关的回答。",
            metadata={"source": "rag_tutorial", "type": "technique"}
        ),
        Document(
            page_content="向量数据库专门用于存储和查询高维向量，支持语义搜索和相似性匹配。",
            metadata={"source": "vector_db_intro", "type": "database"}
        ),
        Document(
            page_content="Supabase是一个开源的Firebase替代品，提供数据库、认证、实时订阅等功能。",
            metadata={"source": "supabase_overview", "type": "platform"}
        ),
    ]
    
    print("1. 创建向量存储并存储文档...")
    vectorstore, retriever, retriever_tool = create_vector_store_and_retriever(
        doc_splits=sample_docs,
        use_supabase=True,
        collection_name="simple_demo"
    )
    
    print("\n2. 测试不同类型的查询...")
    
    queries = [
        "什么是LangChain？",
        "RAG技术的优势是什么？",
        "向量数据库的用途",
        "Supabase提供哪些功能？",
        "如何构建AI应用？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n查询 {i}: {query}")
        results = retriever.invoke(query)
        
        if results:
            print(f"找到 {len(results)} 个相关文档:")
            for j, doc in enumerate(results[:2], 1):  # 显示前2个结果
                print(f"  结果 {j}:")
                print(f"    内容: {doc.page_content[:60]}...")
                print(f"    来源: {doc.metadata.get('source', 'unknown')}")
                print(f"    类型: {doc.metadata.get('type', 'unknown')}")
        else:
            print("  没有找到相关文档")
    
    print(f"\n3. 使用检索工具...")
    tool_result = retriever_tool.invoke({"query": "数据库和框架"})
    print(f"工具查询结果: {tool_result[:150]}...")
    
    print(f"\n4. 检查数据持久化...")
    # 检查文档数量
    if hasattr(vectorstore, 'get_document_count'):
        count = vectorstore.get_document_count()
        print(f"数据库中共有 {count} 个文档")
    
    print(f"\n5. 清理演示数据...")
    if hasattr(vectorstore, 'clear'):
        vectorstore.clear()
        print("✓ 演示数据已清理")
    
    print(f"\n=== 演示完成 ===")
    print("Supabase 向量存储已成功集成到你的 RAG 系统中！")
    print("\n接下来你可以:")
    print("- 在 src/document_processor.py 中使用 use_supabase=True")
    print("- 加载真实的文档数据")
    print("- 在你的应用中使用检索功能")


if __name__ == "__main__":
    main()