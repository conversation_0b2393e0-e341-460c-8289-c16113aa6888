<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>文档上传测试</h1>
    
    <h2>文件上传测试</h2>
    <form id="fileForm">
        <div class="form-group">
            <label for="file">选择文件:</label>
            <input type="file" id="file" name="file" required>
        </div>
        <div class="form-group">
            <label for="fileName">文档名称:</label>
            <input type="text" id="fileName" name="name" required>
        </div>
        <div class="form-group">
            <label for="fileDescription">描述:</label>
            <textarea id="fileDescription" name="description" rows="3"></textarea>
        </div>
        <div class="form-group">
            <label for="fileSubcategory">子类别:</label>
            <select id="fileSubcategory" name="subcategory_id" required>
                <option value="sc_business_design">设计规范</option>
                <option value="sc_business_tech">技术方案</option>
                <option value="sc_personal_ai">人工智能</option>
                <option value="sc_personal_blogs">技术博客</option>
            </select>
        </div>
        <div class="form-group">
            <label for="fileUsername">用户名:</label>
            <input type="text" id="fileUsername" name="username" value="bao">
        </div>
        <button type="submit">上传文件</button>
    </form>
    
    <h2>URL上传测试</h2>
    <form id="urlForm">
        <div class="form-group">
            <label for="url">URL:</label>
            <input type="url" id="url" name="url" required>
        </div>
        <div class="form-group">
            <label for="urlName">文档名称:</label>
            <input type="text" id="urlName" name="name" required>
        </div>
        <div class="form-group">
            <label for="urlDescription">描述:</label>
            <textarea id="urlDescription" name="description" rows="3"></textarea>
        </div>
        <div class="form-group">
            <label for="urlSubcategory">子类别:</label>
            <select id="urlSubcategory" name="subcategory_id" required>
                <option value="sc_business_design">设计规范</option>
                <option value="sc_business_tech">技术方案</option>
                <option value="sc_personal_ai">人工智能</option>
                <option value="sc_personal_blogs">技术博客</option>
            </select>
        </div>
        <div class="form-group">
            <label for="urlUsername">用户名:</label>
            <input type="text" id="urlUsername" name="username" value="bao">
        </div>
        <button type="submit">上传URL</button>
    </form>
    
    <h2>文本上传测试</h2>
    <form id="textForm">
        <div class="form-group">
            <label for="content">文本内容:</label>
            <textarea id="content" name="content" rows="5" required></textarea>
        </div>
        <div class="form-group">
            <label for="textName">文档名称:</label>
            <input type="text" id="textName" name="name" required>
        </div>
        <div class="form-group">
            <label for="textDescription">描述:</label>
            <textarea id="textDescription" name="description" rows="3"></textarea>
        </div>
        <div class="form-group">
            <label for="textSubcategory">子类别:</label>
            <select id="textSubcategory" name="subcategory_id" required>
                <option value="sc_business_design">设计规范</option>
                <option value="sc_business_tech">技术方案</option>
                <option value="sc_personal_ai">人工智能</option>
                <option value="sc_personal_blogs">技术博客</option>
            </select>
        </div>
        <div class="form-group">
            <label for="textUsername">用户名:</label>
            <input type="text" id="textUsername" name="username" value="bao">
        </div>
        <button type="submit">上传文本</button>
    </form>
    
    <div id="result"></div>

    <script>
        const API_BASE = 'http://localhost:8000/api/documents';
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        // 文件上传
        document.getElementById('fileForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('请选择文件', true);
                return;
            }
            
            formData.append('file', file);
            formData.append('name', document.getElementById('fileName').value);
            formData.append('description', document.getElementById('fileDescription').value);
            formData.append('subcategory_id', document.getElementById('fileSubcategory').value);
            formData.append('username', document.getElementById('fileUsername').value);
            
            try {
                const response = await fetch(`${API_BASE}/from-file`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`文件上传成功！文档ID: ${result.document.id}<br>存储路径: ${result.storage.path}`);
                } else {
                    showResult(`上传失败: ${result.detail}`, true);
                }
            } catch (error) {
                showResult(`上传失败: ${error.message}`, true);
            }
        });
        
        // URL上传
        document.getElementById('urlForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const data = {
                url: document.getElementById('url').value,
                name: document.getElementById('urlName').value,
                description: document.getElementById('urlDescription').value,
                subcategory_id: document.getElementById('urlSubcategory').value,
                username: document.getElementById('urlUsername').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/from-url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`URL上传成功！文档ID: ${result.document.id}`);
                } else {
                    showResult(`上传失败: ${result.detail}`, true);
                }
            } catch (error) {
                showResult(`上传失败: ${error.message}`, true);
            }
        });
        
        // 文本上传
        document.getElementById('textForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const data = {
                content: document.getElementById('content').value,
                name: document.getElementById('textName').value,
                description: document.getElementById('textDescription').value,
                subcategory_id: document.getElementById('textSubcategory').value,
                username: document.getElementById('textUsername').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/from-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`文本上传成功！文档ID: ${result.document.id}`);
                } else {
                    showResult(`上传失败: ${result.detail}`, true);
                }
            } catch (error) {
                showResult(`上传失败: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>