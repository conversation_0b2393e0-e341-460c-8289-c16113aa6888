import asyncio
from src.database_service import db_service
from src.mock_data.categories import MOCK_CATEGORIES
from src.mock_data.subcategories import MOCK_SUBCATEGORIES
from src.mock_data.documents import MOCK_DOCUMENTS

async def import_mock_data():
    """Import all mock data into the database."""
    try:
        print("开始导入模拟数据...")
        
        # Import categories
        print(f"导入 {len(MOCK_CATEGORIES)} 个分类...")
        for category in MOCK_CATEGORIES:
            try:
                await db_service.create_category(category)
                print(f"✓ 已导入分类: {category.name}")
            except Exception as e:
                print(f"✗ 导入分类失败 {category.name}: {e}")
        
        # Import subcategories
        print(f"\n导入 {len(MOCK_SUBCATEGORIES)} 个子分类...")
        for subcategory in MOCK_SUBCATEGORIES:
            try:
                await db_service.create_subcategory(subcategory)
                print(f"✓ 已导入子分类: {subcategory.name}")
            except Exception as e:
                print(f"✗ 导入子分类失败 {subcategory.name}: {e}")
        
        # Import documents
        print(f"\n导入 {len(MOCK_DOCUMENTS)} 个文档...")
        for document in MOCK_DOCUMENTS:
            try:
                await db_service.create_document(document)
                print(f"✓ 已导入文档: {document.name}")
            except Exception as e:
                print(f"✗ 导入文档失败 {document.name}: {e}")
        
        print("\n✅ 模拟数据导入完成！")
        
        # Verify import
        categories = await db_service.get_all_categories()
        print(f"\n📊 导入统计:")
        print(f"- 分类数量: {len(categories)}")
        
        total_subcategories = 0
        total_documents = 0
        for category in categories:
            subcategories = await db_service.get_subcategories_by_category(category.id)
            total_subcategories += len(subcategories)
            for subcategory in subcategories:
                documents = await db_service.get_documents_by_subcategory(subcategory.id)
                total_documents += len(documents)
        
        print(f"- 子分类数量: {total_subcategories}")
        print(f"- 文档数量: {total_documents}")
        
    except Exception as e:
        print(f"❌ 导入过程中发生错误: {e}")
    finally:
        await db_service.close()

if __name__ == "__main__":
    asyncio.run(import_mock_data())