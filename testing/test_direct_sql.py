#!/usr/bin/env python3
"""Test direct SQL vector search."""

import sys
import os
import psycopg2
from psycopg2.extras import RealDictCursor

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import config
from src.models import get_embeddings


def test_direct_sql():
    """Test direct SQL vector operations."""
    print("=== 测试直接SQL向量操作 ===\n")
    
    try:
        # Connect to database
        conn = psycopg2.connect(config.SUPABASE_DB_URL)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Create a simple test table
            print("1. 创建测试表...")
            cur.execute("DROP TABLE IF EXISTS vector_test;")
            cur.execute("""
                CREATE TABLE vector_test (
                    id SERIAL PRIMARY KEY,
                    content TEXT,
                    embedding VECTOR(1024)
                );
            """)
            conn.commit()
            print("   ✓ 测试表创建成功")
            
            # Generate test embeddings
            print("\n2. 生成测试嵌入...")
            embeddings = get_embeddings()
            
            test_texts = [
                "这是关于Python编程的文档",
                "这是关于机器学习的文档",
                "这是关于数据库的文档"
            ]
            
            test_embeddings = embeddings.embed_documents(test_texts)
            print(f"   生成了 {len(test_embeddings)} 个嵌入")
            
            # Insert test data
            print("\n3. 插入测试数据...")
            for i, (text, embedding) in enumerate(zip(test_texts, test_embeddings)):
                embedding_str = '[' + ','.join(map(str, embedding)) + ']'
                cur.execute("""
                    INSERT INTO vector_test (content, embedding)
                    VALUES (%s, %s::vector);
                """, (text, embedding_str))
            
            conn.commit()
            print("   ✓ 测试数据插入成功")
            
            # Verify data
            print("\n4. 验证数据...")
            cur.execute("SELECT COUNT(*) as count FROM vector_test;")
            count = cur.fetchone()['count']
            print(f"   表中记录数: {count}")
            
            # Test vector search
            print("\n5. 测试向量搜索...")
            query_text = "编程"
            query_embedding = embeddings.embed_query(query_text)
            query_embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'
            
            # Try different search approaches
            print(f"   查询: '{query_text}'")
            
            # Approach 1: Cosine distance
            print("\n   方法1: 余弦距离")
            try:
                cur.execute("""
                    SELECT id, content, 
                           embedding <=> %s::vector as distance,
                           1 - (embedding <=> %s::vector) as similarity
                    FROM vector_test
                    ORDER BY embedding <=> %s::vector
                    LIMIT 3;
                """, (query_embedding_str, query_embedding_str, query_embedding_str))
                
                results = cur.fetchall()
                print(f"   找到 {len(results)} 个结果:")
                for result in results:
                    print(f"     - 内容: {result['content']}")
                    print(f"       距离: {result['distance']:.4f}")
                    print(f"       相似度: {result['similarity']:.4f}")
                    print()
                    
            except Exception as e:
                print(f"   余弦距离搜索失败: {e}")
            
            # Approach 2: L2 distance
            print("   方法2: L2距离")
            try:
                cur.execute("""
                    SELECT id, content, 
                           embedding <-> %s::vector as l2_distance
                    FROM vector_test
                    ORDER BY embedding <-> %s::vector
                    LIMIT 3;
                """, (query_embedding_str, query_embedding_str))
                
                results = cur.fetchall()
                print(f"   找到 {len(results)} 个结果:")
                for result in results:
                    print(f"     - 内容: {result['content']}")
                    print(f"       L2距离: {result['l2_distance']:.4f}")
                    print()
                    
            except Exception as e:
                print(f"   L2距离搜索失败: {e}")
            
            # Clean up
            print("6. 清理...")
            cur.execute("DROP TABLE vector_test;")
            conn.commit()
            print("   ✓ 清理完成")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            conn.close()


if __name__ == "__main__":
    success = test_direct_sql()
    if success:
        print("\n✅ 直接SQL测试通过！")
    else:
        print("\n❌ 直接SQL测试失败！")
    sys.exit(0 if success else 1)