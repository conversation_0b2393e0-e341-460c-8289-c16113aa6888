# Chunk Service 完整实现指南

## 🎯 解决的问题

你之前的数据结构中，文档chunks通过metadata中的id字段关联父文档：
```json
{
  "id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395",
  "chunk_id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395_chunk_61",
  "chunk_index": 61
}
```

这导致查询某个文档的所有chunks时需要使用：
```sql
SELECT * FROM documents WHERE metadata->>'id' = '2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395'
```

这种JSON路径查询效率低下，不够优雅。

## ✅ 解决方案

### 1. 数据库结构优化

添加专门的`file_id`字段：
```sql
ALTER TABLE public.documents ADD COLUMN IF NOT EXISTS file_id TEXT;
CREATE INDEX IF NOT EXISTS documents_file_id_idx ON public.documents (file_id);
```

### 2. 核心服务实现

创建了专门的`ChunkService`类，提供：
- 按file_id快速检索chunks
- 文档chunk统计信息
- 内容搜索功能
- 兼容旧的metadata查询方式

### 3. API接口

提供RESTful API接口：
- `GET /chunks/document/{file_id}` - 获取文档所有chunks
- `GET /chunks/document/{file_id}/summary` - 获取文档摘要
- `GET /chunks/search` - 搜索chunks
- `GET /chunks/document/{file_id}/chunk/{index}` - 获取特定chunk

## 📁 文件结构

```
src/
├── chunk_service.py              # 核心chunk服务
├── routers/chunks.py             # API路由
├── migrations/
│   └── add_file_id_to_documents.sql  # 数据库迁移
├── examples/
│   └── chunk_usage.py            # 使用示例
├── document_processor.py         # 更新的文档处理器
└── supabase_vectorstore.py       # 更新的向量存储

testing/
└── test_chunk_api.py             # API测试脚本
```

## 🚀 使用方法

### 1. 运行数据库迁移

```bash
# 连接到你的Supabase数据库
psql -d your_database_url -f src/migrations/add_file_id_to_documents.sql
```

### 2. 启动API服务

```bash
cd src
python main.py
```

### 3. 使用API

```bash
# 获取文档所有chunks
curl "http://localhost:8000/chunks/document/2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"

# 获取文档摘要
curl "http://localhost:8000/chunks/document/2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395/summary"

# 搜索chunks
curl "http://localhost:8000/chunks/search?query=Google%20Gemini&limit=5"

# 获取特定chunk
curl "http://localhost:8000/chunks/document/2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395/chunk/61"
```

### 4. Python代码使用

```python
from src.document_processor import get_document_chunks, get_document_chunk_summary
from src.chunk_service import chunk_service

# 获取文档所有chunks
file_id = "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"
chunks = await get_document_chunks(file_id, use_file_id=True)

# 获取文档摘要
summary = await get_document_chunk_summary(file_id)

# 搜索chunks
results = await chunk_service.search_chunks_by_content("Google Gemini", file_id)
```

## 🔧 测试验证

### 1. 运行示例代码
```bash
python src/examples/chunk_usage.py
```

### 2. 运行API测试
```bash
python testing/test_chunk_api.py
```

## 📊 性能对比

| 查询方式 | SQL语句 | 索引类型 | 查询速度 |
|---------|---------|---------|---------|
| 旧方式 | `WHERE metadata->>'id' = $1` | GIN索引 | 较慢 |
| 新方式 | `WHERE file_id = $1` | B-tree索引 | 快速 |

## 🔄 兼容性

- ✅ 同时支持新旧两种查询方式
- ✅ 通过`use_file_id`参数控制
- ✅ 现有代码无需立即修改
- ✅ 渐进式迁移

## 🎉 主要优势

1. **查询效率提升**：直接索引查询 vs JSON路径查询
2. **代码简洁**：`WHERE file_id = $1` vs `WHERE metadata->>'id' = $1`
3. **功能丰富**：提供摘要、搜索、分页等功能
4. **API友好**：RESTful接口，易于集成
5. **向后兼容**：不破坏现有功能

## 📝 API响应示例

### 获取文档chunks
```json
{
  "file_id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395",
  "summary": {
    "total_chunks": 142,
    "avg_chunk_size": 1024,
    "chunk_range": "0-141",
    "title": "building-agents-google-gemini-open-source-frameworks"
  },
  "chunks": [
    {
      "id": "chunk_0",
      "content": "...",
      "metadata": {
        "chunk_index": 0,
        "title": "...",
        "source": "..."
      },
      "created_at": "2025-07-18T15:56:02.178265"
    }
  ],
  "total_chunks": 142
}
```

这个实现为你提供了一个完整、高效、优雅的chunk管理解决方案！🎯