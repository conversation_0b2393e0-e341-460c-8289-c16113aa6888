import asyncio
import pytest
from src.database_service import db_service
from src.schemas.context import Category, SubCategory, Document

class TestDatabaseService:
    """Test database service operations."""
    
    @pytest.mark.asyncio
    async def test_category_crud(self):
        """Test category CRUD operations."""
        # Create test category
        test_category = Category(
            id="test_cat_001",
            name="测试分类",
            type="personal",
            priority="medium",
            status="active",
            lastUpdated="2024-01-01T00:00:00Z",
            description="这是一个测试分类",
            tags=["测试", "分类"]
        )
        
        # Test create
        created = await db_service.create_category(test_category)
        assert created.id == test_category.id
        
        # Test get
        retrieved = await db_service.get_category(test_category.id)
        assert retrieved is not None
        assert retrieved.name == test_category.name
        
        # Test update
        updated = await db_service.update_category(
            test_category.id, 
            {"description": "更新后的描述"}
        )
        assert updated is not None
        assert updated.description == "更新后的描述"
        
        # Test delete
        deleted = await db_service.delete_category(test_category.id)
        assert deleted is True
        
        # Verify deletion
        not_found = await db_service.get_category(test_category.id)
        assert not_found is None
    
    @pytest.mark.asyncio
    async def test_subcategory_crud(self):
        """Test subcategory CRUD operations."""
        # First create a parent category
        parent_category = Category(
            id="test_parent_cat",
            name="父分类",
            type="personal",
            priority="medium",
            status="active",
            lastUpdated="2024-01-01T00:00:00Z",
            description="父分类描述",
            tags=["父分类"]
        )
        await db_service.create_category(parent_category)
        
        # Create test subcategory
        test_subcategory = SubCategory(
            id="test_subcat_001",
            name="测试子分类",
            type="personal",
            categoryId="test_parent_cat",
            priority="medium",
            status="active",
            lastUpdated="2024-01-01T00:00:00Z",
            size="1.0 MB",
            compression=80,
            relevanceScore=0.9,
            tags=["测试", "子分类"],
            description="这是一个测试子分类",
            source="测试来源"
        )
        
        # Test create
        created = await db_service.create_subcategory(test_subcategory)
        assert created.id == test_subcategory.id
        
        # Test get
        retrieved = await db_service.get_subcategory(test_subcategory.id)
        assert retrieved is not None
        assert retrieved.name == test_subcategory.name
        
        # Test get by category
        subcategories = await db_service.get_subcategories_by_category("test_parent_cat")
        assert len(subcategories) == 1
        assert subcategories[0].id == test_subcategory.id
        
        # Cleanup
        await db_service.delete_category("test_parent_cat")
    
    @pytest.mark.asyncio
    async def test_document_crud(self):
        """Test document CRUD operations."""
        # Setup parent structures
        parent_category = Category(
            id="test_doc_cat",
            name="文档分类",
            type="personal",
            priority="medium",
            status="active",
            lastUpdated="2024-01-01T00:00:00Z",
            description="文档分类描述",
            tags=["文档"]
        )
        await db_service.create_category(parent_category)
        
        parent_subcategory = SubCategory(
            id="test_doc_subcat",
            name="文档子分类",
            type="personal",
            categoryId="test_doc_cat",
            priority="medium",
            status="active",
            lastUpdated="2024-01-01T00:00:00Z",
            size="1.0 MB",
            compression=80,
            relevanceScore=0.9,
            tags=["文档"],
            description="文档子分类描述",
            source="测试来源"
        )
        await db_service.create_subcategory(parent_subcategory)
        
        # Create test document
        test_document = Document(
            id="test_doc_001",
            name="测试文档",
            url="https://example.com",
            description="这是一个测试文档",
            createAt="2024-01-01T00:00:00Z",
            lastUpdated="2024-01-01T00:00:00Z",
            size="100 KB",
            tags=["测试", "文档"],
            source="测试来源",
            subcategoryId="test_doc_subcat",
            content="测试文档内容"
        )
        
        # Test create
        created = await db_service.create_document(test_document)
        assert created.id == test_document.id
        
        # Test get
        retrieved = await db_service.get_document(test_document.id)
        assert retrieved is not None
        assert retrieved.name == test_document.name
        
        # Test get by subcategory
        documents = await db_service.get_documents_by_subcategory("test_doc_subcat")
        assert len(documents) == 1
        assert documents[0].id == test_document.id
        
        # Cleanup
        await db_service.delete_category("test_doc_cat")

if __name__ == "__main__":
    asyncio.run(pytest.main([__file__, "-v"]))