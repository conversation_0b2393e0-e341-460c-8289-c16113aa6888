# Chunk Service Documentation

## 概述

Chunk Service 提供了一个优雅的解决方案来管理和检索文档chunks，解决了之前需要通过metadata字段查询的不便问题。

## 问题背景

之前的数据结构中，文档的chunks通过metadata中的id字段关联：
```json
{
  "id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395",
  "chunk_id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395_chunk_61",
  "chunk_index": 61
}
```

这种设计导致查询某个文档的所有chunks时需要使用复杂的JSON查询，效率低下。

## 解决方案

### 方案1：添加file_id字段（推荐）

在documents表中添加专门的`file_id`字段：

```sql
-- 添加file_id字段
ALTER TABLE public.documents ADD COLUMN IF NOT EXISTS file_id TEXT;

-- 创建索引提高查询效率
CREATE INDEX IF NOT EXISTS documents_file_id_idx ON public.documents (file_id);

-- 迁移现有数据
UPDATE public.documents SET file_id = metadata->>'id' WHERE file_id IS NULL;
```

### 优势

1. **查询效率高**：直接通过索引字段查询，避免JSON路径查询
2. **代码简洁**：`WHERE file_id = $1` vs `WHERE metadata->>'id' = $1`
3. **扩展性好**：便于后续添加更多文档级别的字段
4. **兼容性**：保持现有metadata结构不变

## API接口

### 1. 获取文档所有chunks

```http
GET /chunks/document/{file_id}?use_file_id=true
```

**响应示例：**
```json
{
  "file_id": "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395",
  "summary": {
    "total_chunks": 142,
    "avg_chunk_size": 1024,
    "chunk_range": "0-141",
    "title": "building-agents-google-gemini-open-source-frameworks"
  },
  "chunks": [
    {
      "id": "chunk_0",
      "content": "...",
      "metadata": {...},
      "created_at": "2025-07-18T15:56:02.178265"
    }
  ],
  "total_chunks": 142
}
```

### 2. 获取文档摘要

```http
GET /chunks/document/{file_id}/summary
```

### 3. 搜索chunks

```http
GET /chunks/search?query=Google Gemini&file_id={file_id}&limit=10
```

### 4. 获取特定chunk

```http
GET /chunks/document/{file_id}/chunk/{chunk_index}
```

## 使用示例

### Python代码示例

```python
from src.document_processor import get_document_chunks, get_document_chunk_summary

# 获取文档所有chunks
file_id = "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"
chunks = await get_document_chunks(file_id, use_file_id=True)

# 获取文档摘要
summary = await get_document_chunk_summary(file_id)

# 搜索chunks
from src.chunk_service import chunk_service
results = await chunk_service.search_chunks_by_content("Google Gemini", file_id)
```

### API调用示例

```bash
# 获取文档chunks
curl "http://localhost:8000/chunks/document/2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"

# 搜索chunks
curl "http://localhost:8000/chunks/search?query=Google%20Gemini&limit=5"

# 获取特定chunk
curl "http://localhost:8000/chunks/document/2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395/chunk/61"
```

## 迁移步骤

1. **运行数据库迁移**：
   ```bash
   # 执行迁移脚本
   psql -d your_database -f src/migrations/add_file_id_to_documents.sql
   ```

2. **更新应用代码**：
   - 新的chunk插入时设置file_id字段
   - 使用新的API接口进行查询

3. **测试验证**：
   ```bash
   # 运行测试脚本
   python src/examples/chunk_usage.py
   python testing/test_chunk_api.py
   ```

## 性能对比

| 查询方式 | 查询语句 | 索引支持 | 性能 |
|---------|---------|---------|------|
| 旧方式 | `WHERE metadata->>'id' = $1` | GIN索引 | 较慢 |
| 新方式 | `WHERE file_id = $1` | B-tree索引 | 快速 |

## 兼容性

- 新服务同时支持两种查询方式
- 通过`use_file_id`参数控制使用哪种方式
- 现有代码无需立即修改，可以渐进式迁移

## 文件结构

```
src/
├── chunk_service.py          # 核心chunk服务
├── routers/chunks.py         # API路由
├── migrations/               # 数据库迁移脚本
├── examples/chunk_usage.py   # 使用示例
└── document_processor.py     # 更新的文档处理器

testing/
└── test_chunk_api.py         # API测试脚本
```

这个解决方案提供了一个优雅、高效的方式来管理和检索文档chunks，同时保持了良好的向后兼容性。