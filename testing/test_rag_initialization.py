#!/usr/bin/env python3
"""Test RAG service initialization and document storage in Supabase."""

import sys
import os
# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.rag_service import AgenticRAGService
from src.supabase_vectorstore import SupabaseVectorStore
from src.models import get_embeddings

def test_rag_initialization():
    """Test that RAG service properly initializes and stores documents in Supabase."""
    print("=== Testing RAG Service Initialization ===")
    
    # Test URLs (using a subset for faster testing)
    test_urls = [
        "https://lilianweng.github.io/posts/2024-11-28-reward-hacking/",
        "https://blog.langchain.com/context-engineering-for-agents/"
    ]
    
    try:
        # Initialize RAG service
        print("Initializing RAG service...")
        rag_service = AgenticRAGService(urls=test_urls)
        
        # Check if vector store is Supabase
        print(f"Vector store type: {type(rag_service.vectorstore)}")
        
        if isinstance(rag_service.vectorstore, SupabaseVectorStore):
            print("✓ Using Supabase vector store")
            
            # Check document count
            doc_count = rag_service.vectorstore.get_document_count()
            print(f"✓ Documents stored in Supabase: {doc_count}")
            
            if doc_count > 0:
                print("✓ Documents successfully stored in Supabase!")
                
                # Test a simple search
                print("\nTesting document search...")
                results = rag_service.vectorstore.similarity_search("reward hacking", k=2)
                print(f"Search results: {len(results)} documents found")
                
                for i, doc in enumerate(results):
                    print(f"  {i+1}. Content preview: {doc.page_content[:100]}...")
                    print(f"     Metadata: {doc.metadata}")
                
            else:
                print("✗ No documents found in Supabase")
                return False
        else:
            print("✗ Not using Supabase vector store")
            return False
            
        print("\n=== RAG Service Test Completed Successfully ===")
        return True
        
    except Exception as e:
        print(f"✗ Error during RAG initialization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_supabase_storage():
    """Test direct Supabase vector store functionality."""
    print("\n=== Testing Direct Supabase Storage ===")
    
    try:
        # Create embeddings
        embeddings = get_embeddings()
        
        # Create vector store
        vectorstore = SupabaseVectorStore(
            embedding=embeddings,
            table_name="test_documents"
        )
        
        # Clear any existing test data
        vectorstore.clear()
        
        # Add test documents
        test_texts = [
            "This is a test document about artificial intelligence and machine learning.",
            "Another document discussing natural language processing and transformers.",
            "A third document about vector databases and similarity search."
        ]
        
        test_metadata = [
            {"source": "test1", "topic": "AI"},
            {"source": "test2", "topic": "NLP"},
            {"source": "test3", "topic": "databases"}
        ]
        
        print("Adding test documents...")
        ids = vectorstore.add_texts(test_texts, test_metadata)
        print(f"✓ Added {len(ids)} documents with IDs: {ids}")
        
        # Check document count
        count = vectorstore.get_document_count()
        print(f"✓ Document count: {count}")
        
        # Test search
        print("Testing similarity search...")
        results = vectorstore.similarity_search("machine learning", k=2)
        print(f"✓ Found {len(results)} similar documents")
        
        for i, doc in enumerate(results):
            print(f"  {i+1}. {doc.page_content[:50]}...")
            print(f"     Metadata: {doc.metadata}")
        
        # Clean up
        vectorstore.clear()
        print("✓ Cleaned up test data")
        
        print("=== Direct Supabase Storage Test Completed Successfully ===")
        return True
        
    except Exception as e:
        print(f"✗ Error in direct Supabase test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting RAG service and Supabase storage tests...\n")
    
    # Test direct Supabase functionality first
    direct_test_passed = test_direct_supabase_storage()
    
    if direct_test_passed:
        # Test full RAG service initialization
        rag_test_passed = test_rag_initialization()
        
        if rag_test_passed:
            print("\n🎉 All tests passed! RAG service is properly storing documents in Supabase.")
        else:
            print("\n❌ RAG service test failed.")
            sys.exit(1)
    else:
        print("\n❌ Direct Supabase test failed.")
        sys.exit(1)