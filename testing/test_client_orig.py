# test client by langgraph_sdk
# langgraph server by main_orig.py


from langgraph_sdk import get_sync_client
client = get_sync_client(url="http://localhost:2024")
for chunk in client.runs.stream(
    None,  # Threadless run
    "graph", # graph 名称
    input={
        "messages": [
            {"role": "user", "content": "请总结一下Lilian Weng关于幻觉（hallucination）的观点。"}
        ]
    },
    stream_mode="messages-tuple",
):
    print(chunk.data)