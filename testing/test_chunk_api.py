"""Test script for chunk API endpoints."""

import requests
import json
from typing import Dict, Any


class ChunkAPITester:
    """Test client for chunk API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_get_document_chunks(self, file_id: str, use_file_id: bool = True) -> Dict[str, Any]:
        """Test getting all chunks for a document."""
        url = f"{self.base_url}/chunks/document/{file_id}"
        params = {"use_file_id": use_file_id}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}
    
    def test_get_document_summary(self, file_id: str) -> Dict[str, Any]:
        """Test getting document summary."""
        url = f"{self.base_url}/chunks/document/{file_id}/summary"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}
    
    def test_search_chunks(self, query: str, file_id: str = None, limit: int = 10) -> Dict[str, Any]:
        """Test searching chunks."""
        url = f"{self.base_url}/chunks/search"
        params = {"query": query, "limit": limit}
        if file_id:
            params["file_id"] = file_id
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}
    
    def test_get_specific_chunk(self, file_id: str, chunk_index: int, use_file_id: bool = True) -> Dict[str, Any]:
        """Test getting a specific chunk by index."""
        url = f"{self.base_url}/chunks/document/{file_id}/chunk/{chunk_index}"
        params = {"use_file_id": use_file_id}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": str(e)}


def run_tests():
    """Run all chunk API tests."""
    tester = ChunkAPITester()
    
    # Example file ID from your metadata
    file_id = "2a9bbdd1-f9b4-4f49-a1d6-a4b93e3ce395"
    
    print("=== Chunk API Tests ===\n")
    
    # Test 1: Get document summary
    print("1. Testing document summary...")
    summary = tester.test_get_document_summary(file_id)
    print(f"Result: {json.dumps(summary, indent=2)}\n")
    
    # Test 2: Get all chunks (legacy approach)
    print("2. Testing get all chunks (metadata approach)...")
    chunks_legacy = tester.test_get_document_chunks(file_id, use_file_id=False)
    if "error" not in chunks_legacy:
        print(f"Found {chunks_legacy.get('total_chunks', 0)} chunks")
        print(f"Summary: {chunks_legacy.get('summary', {})}")
    else:
        print(f"Error: {chunks_legacy['error']}")
    print()
    
    # Test 3: Get all chunks (new approach - will work after migration)
    print("3. Testing get all chunks (file_id approach)...")
    chunks_new = tester.test_get_document_chunks(file_id, use_file_id=True)
    if "error" not in chunks_new:
        print(f"Found {chunks_new.get('total_chunks', 0)} chunks")
    else:
        print(f"Error: {chunks_new['error']} (Expected if migration not run yet)")
    print()
    
    # Test 4: Search chunks
    print("4. Testing chunk search...")
    search_results = tester.test_search_chunks("Google Gemini", file_id=file_id, limit=3)
    if "error" not in search_results:
        print(f"Found {search_results.get('total_results', 0)} matching chunks")
        for i, result in enumerate(search_results.get('results', [])[:2]):
            print(f"   Result {i+1}: {result.get('content', '')[:80]}...")
    else:
        print(f"Error: {search_results['error']}")
    print()
    
    # Test 5: Get specific chunk
    print("5. Testing get specific chunk...")
    specific_chunk = tester.test_get_specific_chunk(file_id, 61, use_file_id=False)
    if "error" not in specific_chunk:
        chunk_data = specific_chunk.get('chunk', {})
        print(f"Chunk 61 content preview: {chunk_data.get('content', '')[:100]}...")
    else:
        print(f"Error: {specific_chunk['error']}")


if __name__ == "__main__":
    run_tests()