#!/usr/bin/env python3
"""Debug search functionality in Supabase vector store."""

import sys
import os
import psycopg2
from psycopg2.extras import RealDictCursor

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import config
from src.models import get_embeddings


def debug_search():
    """Debug the search functionality."""
    print("=== 调试搜索功能 ===\n")
    
    try:
        # Connect to database
        conn = psycopg2.connect(config.SUPABASE_DB_URL)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Check current data
            print("1. 检查当前数据...")
            cur.execute("SELECT COUNT(*) as count FROM document_embedding;")
            total_count = cur.fetchone()['count']
            print(f"   总文档数: {total_count}")
            
            if total_count > 0:
                # Show all data
                cur.execute("""
                    SELECT id, content, metadata, 
                           CASE WHEN embedding IS NOT NULL THEN 'YES' ELSE 'NO' END as has_embedding
                    FROM document_embedding 
                    ORDER BY created_at DESC;
                """)
                all_docs = cur.fetchall()
                
                print("   所有文档:")
                for i, doc in enumerate(all_docs, 1):
                    print(f"     {i}. ID: {doc['id']}")
                    print(f"        内容: {doc['content']}")
                    print(f"        元数据: {doc['metadata']}")
                    print(f"        有嵌入: {doc['has_embedding']}")
                    print()
                
                # Test embedding generation
                print("2. 测试嵌入生成...")
                embeddings = get_embeddings()
                test_query = "Supabase"
                query_embedding = embeddings.embed_query(test_query)
                print(f"   查询 '{test_query}' 的嵌入维度: {len(query_embedding)}")
                
                # Test raw vector search
                print("\n3. 测试原始向量搜索...")
                try:
                    search_sql = """
                    SELECT id, content, metadata, 
                           1 - (embedding <=> %s::vector) as similarity
                    FROM document_embedding
                    WHERE embedding IS NOT NULL
                    ORDER BY embedding <=> %s::vector
                    LIMIT 3;
                    """
                    
                    cur.execute(search_sql, (query_embedding, query_embedding))
                    results = cur.fetchall()
                    
                    print(f"   原始搜索结果数: {len(results)}")
                    for result in results:
                        print(f"     - 相似度: {result['similarity']:.4f}")
                        print(f"       内容: {result['content'][:50]}...")
                        print()
                        
                except Exception as e:
                    print(f"   原始搜索失败: {e}")
                
                # Test different search terms
                print("4. 测试不同搜索词...")
                search_terms = ["Python", "机器学习", "数据库", "编程", "技术"]
                
                for term in search_terms:
                    term_embedding = embeddings.embed_query(term)
                    try:
                        cur.execute(search_sql, (term_embedding, term_embedding))
                        term_results = cur.fetchall()
                        print(f"   '{term}': {len(term_results)} 个结果")
                        if term_results:
                            best_match = term_results[0]
                            print(f"     最佳匹配 (相似度: {best_match['similarity']:.4f}): {best_match['content'][:30]}...")
                    except Exception as e:
                        print(f"   '{term}' 搜索失败: {e}")
                
                # Check embedding dimensions
                print("\n5. 检查嵌入维度...")
                cur.execute("""
                    SELECT id, array_length(embedding::float[], 1) as dim
                    FROM document_embedding 
                    WHERE embedding IS NOT NULL 
                    LIMIT 3;
                """)
                dim_results = cur.fetchall()
                
                for result in dim_results:
                    print(f"   文档 {result['id']}: 维度 {result['dim']}")
            
            else:
                print("   没有找到任何文档")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()


if __name__ == "__main__":
    debug_search()