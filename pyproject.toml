[project]
name = "agnetic-rag"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "bs4>=0.0.2",
    "fastapi>=0.116.1",
    "langchain-community>=0.3.27",
    "langchain-ollama>=0.3.3",
    "langchain-text-splitters>=0.3.8",
    "langchain[openai]>=0.3.26",
    "langgraph>=0.5.1",
    "langgraph-cli[inmem]>=0.3.3",
    "langgraph-sdk>=0.1.72",
    "pydantic>=2.11.7",
    "uvicorn[standard]>=0.35.0",
    "supabase>=2.9.1",
    "psycopg2-binary>=2.9.9",
    "asyncpg>=0.30.0",
    "python-multipart>=0.0.20",
]
