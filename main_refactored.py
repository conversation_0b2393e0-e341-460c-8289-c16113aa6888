"""Refactored main module using modular components."""

from src.rag_service import get_rag_service


def main():
    """Main function demonstrating the RAG service."""
    print("Hello from agnetic-rag!")
    
    # Get the RAG service
    service = get_rag_service()
    
    # Test query
    question = "What does <PERSON><PERSON> say about types of reward hacking?"
    print(f"\nQuery: {question}")
    print("=" * 50)
    
    # Stream results
    for chunk in service.query(question):
        for node, update in chunk.items():
            print(f"Update from node {node}")
            if "messages" in update and update["messages"]:
                msg = update["messages"][-1]
                if hasattr(msg, "pretty_print"):
                    msg.pretty_print()
                else:
                    print(msg)
            print("\n")


def test_agentic_rag():
    """Test the Agentic RAG workflow with multiple test cases."""
    print("=== 开始 Agentic RAG 测试 ===")
    
    service = get_rag_service()
    
    test_cases = [
        "What does <PERSON><PERSON> say about types of reward hacking?",
        "请总结一下<PERSON><PERSON>g关于幻觉（hallucination）的观点。",
        "Diffusion video的核心思想是什么？",
        "你好！",
    ]
    
    for idx, question in enumerate(test_cases):
        print(f"\n--- 测试用例 {idx+1}: {question} ---")
        
        for chunk in service.query(question):
            for node, update in chunk.items():
                print(f"Update from node {node}")
                if "messages" in update and update["messages"]:
                    msg = update["messages"][-1]
                    if hasattr(msg, "pretty_print"):
                        msg.pretty_print()
                    else:
                        print(msg)
                print("\n")


if __name__ == "__main__":
    test_agentic_rag()