import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Eye, Edit3, MoreH<PERSON><PERSON><PERSON>, Clock, Layers, Zap, ZapOff } from 'lucide-react';

interface Document {
    id: string;
    name: string;
    type: string;
    category: string;
    subCategory: string;
    subCategoryId: string; // 🆕 子类别id
    description: string;
    source: string;
    lastUpdated: string;
    size: string;
    priority: 'high' | 'medium' | 'low';
    status: string;
    relevanceScore: number;
    compression: number;
    tags: string[];
    knowledgeGraph?: boolean;
    url?: string;
    metadata?: Record<string, any>;
    // 🆕 添加向量化状态字段
    isVectorized?: boolean;
    vectorizedAt?: string;
}

interface DocumentListProps {
    documents: Document[];
    getDocumentTypeIcon: (type: string, category: string) => React.ReactNode;
    getPriorityColor: (priority: string) => string;
    getStatusIcon: (status: string) => React.ReactNode;
    onDocumentUpdate?: (docId: string, updates: Partial<Document>) => void;
}

// 格式化相对时间
const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
        return '刚刚'
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes}分钟前`
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours}小时前`
    } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400)
        return `${days}天前`
    } else if (diffInSeconds < 31536000) {
        const months = Math.floor(diffInSeconds / 2592000)
        return `${months}个月前`
    } else {
        const years = Math.floor(diffInSeconds / 31536000)
        return `${years}年前`
    }
}

export const DocumentList: React.FC<DocumentListProps> = ({
    documents,
    getDocumentTypeIcon,
    getPriorityColor,
    getStatusIcon,
    onDocumentUpdate,
}) => {
    const [vectorizingDocs, setVectorizingDocs] = useState<Set<string>>(new Set());

    // 处理向量化操作
    const handleVectorize = async (docId: string) => {
        if (vectorizingDocs.has(docId)) return; // 防止重复点击

        setVectorizingDocs(prev => new Set(prev).add(docId));

        try {
            console.log('开始向量化文档:', docId);

            // 获取文档对象
            const doc = documents.find(d => d.id === docId);
            if (!doc) {
                throw new Error('文档不存在');
            }

            let response;
            let result;

            // 如果文档包含URL，则调用从URL创建文档的API
            if (doc.url) {
                console.log('从URL创建并向量化文档:', doc.url);

                // 调用从URL创建文档的API
                response = await fetch(`/api/embedding/document/from-url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: doc.url,
                        subcategory_id: doc.subCategoryId, // 用id
                        name: doc.name,
                        description: doc.description
                    }),
                });
            } else {
                // 调用普通向量化API
                response = await fetch(`/api/documents/${docId}/vectorize`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
            }

            if (!response.ok) {
                throw new Error(`向量化失败: ${response.statusText}`);
            }

            result = await response.json();
            console.log('向量化成功:', result);

            // 更新文档状态
            const now = new Date().toLocaleString('zh-CN');
            if (onDocumentUpdate) {
                onDocumentUpdate(docId, {
                    isVectorized: true,
                    vectorizedAt: now,
                });
            }

        } catch (error) {
            console.error('向量化失败:', error);
            // 可以在这里添加错误提示，比如使用 toast 组件
            alert(`向量化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        } finally {
            setVectorizingDocs(prev => {
                const newSet = new Set(prev);
                newSet.delete(docId);
                return newSet;
            });
        }
    };
    if (documents.length === 0) {
        return (
            <div className="text-center py-12">
                <img src="/spider-web.svg" alt="Spider Web" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的文档</h3>
                <p className="text-gray-600">尝试调整搜索条件或添加新的文档</p>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">文档名称</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">描述</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">状态</th>
                            {/* <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">优先级</th> */}
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">大小</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">更新时间</th>
                            <th className="text-center py-3 px-4 text-sm font-medium text-gray-700">向量化</th>
                            <th className="text-right py-3 px-4 text-sm font-medium text-gray-700">操作</th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                        {documents.map((doc) => (
                            <tr key={doc.id} className="hover:bg-gray-50 transition-colors">
                                {/* 文档名称 */}
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-2">
                                        {getDocumentTypeIcon(doc.type, doc.category)}
                                        <div className="min-w-0 flex-1">
                                            <div className="flex items-center gap-2">
                                                <h3 className="text-sm font-medium text-gray-900 truncate">{doc.name}</h3>
                                                {doc.knowledgeGraph && (
                                                    <span className="px-1.5 py-0.5 text-xs bg-purple-100 text-purple-700 rounded shrink-0">
                                                        图谱
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                {/* 描述 */}
                                <td className="py-3 px-4 w-48">
                                    <p className="text-sm text-gray-600 line-clamp-2 max-w-44" title={doc.description}>
                                        {doc.description}
                                    </p>
                                </td>

                                {/* 状态 */}
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-1">
                                        {getStatusIcon(doc.status)}
                                        {/* <span className="text-sm text-gray-700">{doc.status}</span> */}
                                    </div>
                                </td>


                                {/* 大小 */}
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-1 text-sm text-gray-700">
                                        <Layers className="w-3 h-3" />
                                        {doc.size}
                                    </div>
                                </td>

                                {/* 更新时间 */}
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-1 text-sm text-gray-700">
                                        <Clock className="w-3 h-3" />
                                        {formatRelativeTime(doc.lastUpdated)}
                                    </div>
                                </td>

                                {/* 向量化状态 - 只显示图标 */}
                                <td className="py-3 px-4 text-center">
                                    {doc.isVectorized ? (
                                        <div title={`已向量化 (${doc.vectorizedAt})`}>
                                            <Zap className="w-4 h-4 text-green-600 mx-auto" />
                                        </div>
                                    ) : vectorizingDocs.has(doc.id) ? (
                                        <div title="正在向量化...">
                                            <Zap className="w-4 h-4 text-blue-600 mx-auto animate-spin" />
                                        </div>
                                    ) : (
                                        <div
                                            title="点击向量化此文档"
                                            onClick={() => {
                                                console.log('向量化文档id:', doc.id, 'subCategoryId:', doc.subCategoryId);
                                                handleVectorize(doc.id);
                                            }}
                                            className="cursor-pointer"
                                        >
                                            <ZapOff className="w-4 h-4 text-gray-400 mx-auto hover:text-blue-600 transition-colors" />
                                        </div>
                                    )}
                                </td>

                                {/* 操作 */}
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-1 justify-end">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className={`h-7 w-7 p-0 ${doc.url ? 'hover:bg-blue-50' : 'cursor-not-allowed'}`}
                                            onClick={() => doc.url && window.open(doc.url, '_blank')}
                                            disabled={!doc.url}
                                            title={doc.url ? `查看原始资料: ${doc.url}` : '暂无链接'}
                                        >
                                            <Eye className={`w-3.5 h-3.5 ${doc.url ? 'text-green-600 hover:text-blue-700' : 'text-gray-400'}`} />
                                        </Button>
                                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                                            <Edit3 className="w-3.5 h-3.5" />
                                        </Button>
                                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                                            <MoreHorizontal className="w-3.5 h-3.5" />
                                        </Button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};