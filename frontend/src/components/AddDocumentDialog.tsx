import React, { useState, useEffect } from 'react'
import {
  Upload, Link, FileText, BookOpen, Eye,
  CheckCircle, Loader2, X, Plus,
  File, Image, Video, Music, Archive
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { DocumentPreview } from './DocumentPreview'

interface AddDocumentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onDocumentAdd?: (document: any) => void
}

type DocumentType = 'file' | 'url' | 'text'
type FileType = 'pdf' | 'markdown' | 'text' | 'image' | 'video' | 'audio' | 'archive' | 'other'

interface DocumentForm {
  type: DocumentType
  name: string
  description: string
  category: 'business' | 'personal'
  subCategory: string
  tags: string[]
  file?: File
  url?: string
  content?: string
}

// 在文件顶部添加映射表
const subcategoryMapping = {
  // 业务类别映射
  '研发设计': 'sc_business_design',
  '生产管控': 'sc_business_production',
  '运维数据': 'sc_business_mro',
  '企业标准': 'sc_business_standard',
  '其他文档': 'sc_business_other',

  // 个人类别映射  
  '学术论文': 'sc_personal_paper',
  '网络资源': 'sc_personal_blogs',
  '个人笔记': 'sc_personal_note',
  '其他资料': 'sc_personal_other'
}

const businessCategories = [
  '研发设计', '生产管控', '运维数据', '企业标准', '其他文档'
]

const personalCategories = [
  '学术论文', '网络资源', '个人笔记', '其他资料'
]

function getFileType(file: File): FileType {
  const extension = file.name.split('.').pop()?.toLowerCase()
  const mimeType = file.type.toLowerCase()

  if (mimeType.includes('pdf') || extension === 'pdf') return 'pdf'
  if (mimeType.includes('markdown') || ['md', 'markdown'].includes(extension || '')) return 'markdown'
  if (mimeType.includes('text') || ['txt', 'doc', 'docx'].includes(extension || '')) return 'text'
  if (mimeType.includes('image')) return 'image'
  if (mimeType.includes('video')) return 'video'
  if (mimeType.includes('audio')) return 'audio'
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension || '')) return 'archive'
  return 'other'
}

function getFileIcon(fileType: FileType) {
  switch (fileType) {
    case 'pdf': return <FileText className="w-8 h-8 text-red-500" />
    case 'markdown': return <BookOpen className="w-8 h-8 text-blue-500" />
    case 'text': return <File className="w-8 h-8 text-gray-500" />
    case 'image': return <Image className="w-8 h-8 text-green-500" />
    case 'video': return <Video className="w-8 h-8 text-purple-500" />
    case 'audio': return <Music className="w-8 h-8 text-orange-500" />
    case 'archive': return <Archive className="w-8 h-8 text-yellow-500" />
    default: return <File className="w-8 h-8 text-gray-500" />
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// DocumentPreview component has been extracted to its own file

export function AddDocumentDialog({ open, onOpenChange, onDocumentAdd }: AddDocumentDialogProps) {
  // 定义初始表单状态
  const initialFormState: DocumentForm = {
    type: 'file',
    name: '',
    description: '',
    category: 'business',
    subCategory: '',
    tags: []
  }

  const [form, setForm] = useState<DocumentForm>(initialFormState)
  const [tagInput, setTagInput] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [autoPreview, setAutoPreview] = useState(true)

  // 监听文档类型变化，重置表单到初始状态
  useEffect(() => {
    setForm(prev => {
      // 根据不同类型设置不同的默认值
      let defaultCategory: 'business' | 'personal' = 'business'
      let defaultSubCategory = ''

      if (prev.type === 'url') {
        defaultCategory = 'personal'
        defaultSubCategory = '网络资源'
      } else if (prev.type === 'file') {
        defaultCategory = 'business'
        defaultSubCategory = ''
      }

      return {
        ...initialFormState,
        type: prev.type,
        category: defaultCategory,
        subCategory: defaultSubCategory
      }
    })

    // 同时重置其他状态
    setTagInput('')
    setDragActive(false)
  }, [form.type])

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]
      setForm(prev => ({
        ...prev,
        file,
        name: prev.name || file.name.replace(/\.[^/.]+$/, "")
      }))
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      const fileName = file.name.replace(/\.[^/.]+$/, "") // 去掉扩展名
      setForm(prev => ({
        ...prev,
        file,
        name: prev.name || fileName // 只在名称为空时自动填充
      }))
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !form.tags.includes(tagInput.trim())) {
      setForm(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    console.log('开始提交文档:', {
      type: form.type,
      name: form.name,
      description: form.description,
      category: form.category,
      subCategory: form.subCategory,
      url: form.url,
      file: form.file?.name,
      content: form.content?.substring(0, 100) + '...'
    })

    try {
      let response
      let newDocument

      if (form.type === 'url' && form.url) {
        console.log('提交URL文档:', form.url)

        // 将子类别名称映射为ID
        const subcategoryId = subcategoryMapping[form.subCategory as keyof typeof subcategoryMapping] || form.subCategory

        const requestData = {
          url: form.url,
          subcategory_id: subcategoryId, // 使用映射后的ID
          name: form.name,
          description: form.description
        }

        console.log('发送请求数据:', requestData)

        response = await fetch('http://localhost:8000/api/embedding/document/from-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        })

        console.log('API响应状态:', response.status, response.statusText)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('API错误响应:', errorText)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
        }

        const result = await response.json()
        console.log('API成功响应:', result)

        // 根据API响应格式构建文档对象
        newDocument = {
          id: result.document?.id || Date.now().toString(),
          name: result.document?.name || form.name,
          description: result.document?.description || form.description,
          type: form.category,
          category: result.document?.subcategoryId || form.subCategory || '未分类',
          subCategory: result.document?.subcategoryId || form.subCategory || '未分类',
          status: result.isVectorized ? 'completed' : 'processing',
          lastUpdated: result.document?.lastUpdated || new Date().toISOString(),
          size: result.document?.size || '未知',
          compression: Math.floor(Math.random() * 50) + 50,
          relevanceScore: Math.random(),
          tags: result.document?.tags || form.tags,
          source: result.document?.url || form.url,
          knowledgeGraph: result.isVectorized || false,
          totalChunks: result.totalChunks || 0,
          vectorizedAt: result.vectorizedAt
        }

      } // 在文件上传部分
      else if (form.type === 'file' && form.file) {
        console.log('提交文件文档:', form.file.name)

        const subcategoryId = subcategoryMapping[form.subCategory as keyof typeof subcategoryMapping] || form.subCategory

        const formData = new FormData()
        formData.append('file', form.file)
        formData.append('subcategory_id', subcategoryId) // 使用映射后的ID
        formData.append('name', form.name)
        formData.append('description', form.description)

        console.log('发送文件数据:', {
          fileName: form.file.name,
          fileSize: form.file.size,
          subcategory_id: form.subCategory,
          name: form.name,
          description: form.description
        })

        response = await fetch('http://localhost:8000/api/documents/from-file', {
          method: 'POST',
          body: formData
        })

        console.log('文件API响应状态:', response.status, response.statusText)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('文件API错误响应:', errorText)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
        }

        const result = await response.json()
        console.log('文件API成功响应:', result)

        newDocument = {
          id: result.document?.id || Date.now().toString(),
          name: result.document?.name || form.name,
          description: result.document?.description || form.description,
          type: form.category,
          category: result.document?.subcategoryId || form.subCategory || '未分类',
          subCategory: result.document?.subcategoryId || form.subCategory || '未分类',
          status: result.isVectorized ? 'completed' : 'processing',
          lastUpdated: result.document?.lastUpdated || new Date().toISOString(),
          size: result.document?.size || formatFileSize(form.file.size),
          compression: Math.floor(Math.random() * 50) + 50,
          relevanceScore: Math.random(),
          tags: result.document?.tags || form.tags,
          source: form.file.name,
          knowledgeGraph: result.isVectorized || false,
          totalChunks: result.totalChunks || 0,
          vectorizedAt: result.vectorizedAt
        }

      } // 在文本提交部分也做同样的修改
      else if (form.type === 'text' && form.content) {
        const subcategoryId = subcategoryMapping[form.subCategory as keyof typeof subcategoryMapping] || form.subCategory

        const requestData = {
          content: form.content,
          subcategory_id: subcategoryId, // 使用映射后的ID
          name: form.name,
          description: form.description
        }

        console.log('发送文本数据:', requestData)

        response = await fetch('http://localhost:8000/api/documents/from-text', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        })

        console.log('文本API响应状态:', response.status, response.statusText)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('文本API错误响应:', errorText)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
        }

        const result = await response.json()
        console.log('文本API成功响应:', result)

        newDocument = {
          id: result.document?.id || Date.now().toString(),
          name: result.document?.name || form.name,
          description: result.document?.description || form.description,
          type: form.category,
          category: result.document?.subcategoryId || form.subCategory || '未分类',
          subCategory: result.document?.subcategoryId || form.subCategory || '未分类',
          status: result.isVectorized ? 'completed' : 'processing',
          lastUpdated: result.document?.lastUpdated || new Date().toISOString(),
          size: result.document?.size || '未知',
          compression: Math.floor(Math.random() * 50) + 50,
          relevanceScore: Math.random(),
          tags: result.document?.tags || form.tags,
          source: '文本输入',
          knowledgeGraph: result.isVectorized || false,
          totalChunks: result.totalChunks || 0,
          vectorizedAt: result.vectorizedAt
        }
      }

      console.log('构建的文档对象:', newDocument)

      if (newDocument) {
        onDocumentAdd?.(newDocument)
        console.log('文档添加成功，关闭对话框')
        onOpenChange(false)

        // 重置表单
        setForm({
          type: 'file',
          name: '',
          description: '',
          category: 'business',
          subCategory: '',
          tags: []
        })
        console.log('表单已重置')
      }

    } catch (error) {
      console.error('添加文档失败:', error)
      console.error('错误详情:', {
        message: (error as Error).message,
        stack: (error as Error).stack
      })
      // 这里可以添加用户友好的错误提示
      alert(`添加文档失败: ${(error as Error).message}`)
    } finally {
      setIsSubmitting(false)
      console.log('提交流程结束')
    }
  }

  const categories = form.category === 'business' ? businessCategories : personalCategories

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`max-w-none ${autoPreview ? 'w-[80vw]' : 'w-[35vw]'} h-[90vh] p-0 overflow-hidden sm:max-w-none flex flex-col`}>
        <DialogHeader className="px-10 pt-8 pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5 text-purple-600" />
                添加新文档
              </DialogTitle>
              <DialogDescription>
                支持上传文件、添加网页链接或直接输入文本内容，系统将自动处理并建立知识图谱
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">自动预览</span>
              <Switch
                checked={autoPreview}
                onCheckedChange={setAutoPreview}
              />
            </div>
          </div>
        </DialogHeader>

        {/* 主要内容区域 */}
        <div className="flex flex-row gap-10 px-10 flex-1 overflow-hidden">
          {/* 左侧表单 */}
          <div className={`${autoPreview ? 'w-2/5' : 'w-full'} bg-white rounded-xl shadow-lg p-6 overflow-y-auto flex flex-col`}>
            <form onSubmit={handleSubmit} className="space-y-6 flex-1">

              {/* 文档类型选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  文档类型
                </label>
                <div className="grid grid-cols-3 gap-3">
                  <button
                    type="button"
                    onClick={() => setForm(prev => ({
                      ...prev,
                      type: 'file',
                      category: 'business'
                    }))}
                    className={`p-4 rounded-lg border-2 transition-all ${form.type === 'file'
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <Upload className="w-6 h-6 mx-auto mb-2" />
                    <div className="text-sm font-medium">上传文件</div>
                    <div className="text-xs text-gray-500">PDF, Word, Markdown等</div>
                  </button>

                  <button
                    type="button"
                    onClick={() => setForm(prev => ({
                      ...prev,
                      type: 'url',
                      category: 'personal',
                      subCategory: '网络资源'
                    }))}
                    className={`p-4 rounded-lg border-2 transition-all ${form.type === 'url'
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <Link className="w-6 h-6 mx-auto mb-2" />
                    <div className="text-sm font-medium">网页链接</div>
                    <div className="text-xs text-gray-500">在线文档、网页等</div>
                  </button>

                  <button
                    type="button"
                    onClick={() => setForm(prev => ({ ...prev, type: 'text' }))}
                    className={`p-4 rounded-lg border-2 transition-all ${form.type === 'text'
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300'
                      }`}
                  >
                    <FileText className="w-6 h-6 mx-auto mb-2" />
                    <div className="text-sm font-medium">文本内容</div>
                    <div className="text-xs text-gray-500">直接输入文本</div>
                  </button>
                </div>
              </div>

              {/* 文档输入区域 */}
              <div>
                {form.type === 'file' && (
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${dragActive
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-300 hover:border-gray-400'
                      }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    {form.file ? (
                      <div className="flex items-center gap-3 justify-center">
                        {getFileIcon(getFileType(form.file))}
                        <div>
                          <p className="font-medium text-gray-900">{form.file.name}</p>
                          <p className="text-sm text-gray-500">{formatFileSize(form.file.size)}</p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setForm(prev => ({ ...prev, file: undefined }))}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-lg font-medium text-gray-900 mb-2">
                          拖拽文件到此处或点击选择
                        </p>
                        <p className="text-gray-500 mb-4">
                          支持 PDF, Word, Markdown, 图片等格式
                        </p>
                        <input
                          type="file"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="file-upload"
                          accept=".pdf,.doc,.docx,.md,.txt,.jpg,.jpeg,.png,.gif"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById('file-upload')?.click()}
                        >
                          选择文件
                        </Button>
                      </>
                    )}
                  </div>
                )}

                {form.type === 'url' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      网页链接
                    </label>
                    <Input
                      type="url"
                      placeholder="https://example.com/document"
                      value={form.url || ''}
                      onChange={(e) => {
                        const url = e.target.value
                        let urlName = ''
                        if (url) {
                          const pathParts = url.split('/')
                          const lastPart = pathParts[pathParts.length - 1]
                          urlName = lastPart.split('?')[0].split('#')[0] || ''
                          if (!urlName && pathParts.length > 1) {
                            urlName = pathParts[pathParts.length - 2] || ''
                          }
                        }
                        setForm(prev => ({
                          ...prev,
                          url,
                          name: !prev.name ? urlName : prev.name
                        }))
                      }}
                      className="w-full"
                    />
                  </div>
                )}

                {form.type === 'text' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文本内容
                    </label>
                    <Textarea
                      placeholder="请输入文档内容..."
                      value={form.content || ''}
                      onChange={(e) => {
                        const content = e.target.value
                        const textName = content ? content.substring(0, 10) : ''
                        setForm(prev => ({
                          ...prev,
                          content,
                          name: !prev.name ? textName : prev.name
                        }))
                      }}
                      className="w-full min-h-32"
                    />
                  </div>
                )}
              </div>

              {/* 基本信息 - 移除优先级字段 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文档名称 *
                </label>
                <Input
                  type="text"
                  placeholder="输入文档名称"
                  value={form.name}
                  onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文档类别
                  </label>
                  <select
                    value={form.category}
                    onChange={(e) => setForm(prev => ({
                      ...prev,
                      category: e.target.value as any,
                      subCategory: ''
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="business">业务文档</option>
                    <option value="personal">个人知识库</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    子类别
                  </label>
                  <select
                    value={form.subCategory}
                    onChange={(e) => setForm(prev => ({ ...prev, subCategory: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">选择子类别</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 文档描述 - 放大 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文档描述
                  <span className="text-xs text-gray-500 ml-2">(可由AI自动生成)</span>
                </label>
                <Textarea
                  placeholder="简要描述文档内容和用途，或留空让AI自动生成..."
                  value={form.description}
                  onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full min-h-24"
                  rows={4}
                />
              </div>

              {/* 标签 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  标签
                  <span className="text-xs text-gray-500 ml-2">(可由AI自动生成)</span>
                </label>
                <div className="flex gap-2 mb-2">
                  <Input
                    type="text"
                    placeholder="添加标签或留空让AI自动生成"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    className="flex-1"
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    添加
                  </Button>
                </div>
                {form.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {form.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-700 rounded-md text-sm"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="hover:text-purple-900"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* 右侧预览 - 条件渲染 */}
          {autoPreview && (
            <div className="flex-[1.4] bg-gray-50 rounded-xl border p-6 shadow overflow-y-auto min-w-[340px] flex flex-col">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2 border-b pb-2">
                  <Eye className="w-5 h-5" />
                  文档预览
                </h3>
              </div>
              <div className="flex-1">
                {(form.file || form.url || form.content) ? (
                  <DocumentPreview form={form} />
                ) : (
                  <div className="flex items-center justify-center h-48 text-gray-400">
                    <span>请先上传文档或输入内容</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 固定的Footer提交按钮 */}
        <div className="border-t bg-white px-10 py-4 mt-auto">
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={!form.name || isSubmitting || (!form.file && !form.url && !form.content)}
              className="flex-1 bg-purple-600 hover:bg-purple-700"
              onClick={handleSubmit}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  添加文档
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}