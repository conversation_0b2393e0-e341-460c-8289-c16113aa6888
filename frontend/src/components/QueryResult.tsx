interface QueryResultProps {
  result?: {
    answer: string
    sources: Array<{ content?: string } | string>
  } | null
  error?: string | null
}

export const QueryResult = ({ result, error }: QueryResultProps) => {
  if (!result && !error) return null

  return (
    <div className="w-full mb-8 p-6 bg-gray-50 rounded-lg">
      {error && (
        <div className="text-red-600 mb-4">
          <strong>错误:</strong> {error}
        </div>
      )}
      
      {result && (
        <div className="space-y-4">
          <div className="text-lg font-semibold text-gray-900">查询结果:</div>
          
          {result.answer && (
            <div className="bg-white p-4 rounded border">
              {/* <div className="font-medium text-gray-700 mb-2">回答:</div> */}
              <div className="text-gray-900 whitespace-pre-wrap">{result.answer}</div>
            </div>
          )}
          
          {result.sources && result.sources.length > 0 && (
            <div className="bg-white p-4 rounded border">
              <div className="font-medium text-gray-700 mb-2">相关文档:</div>
              <ul className="space-y-2">
                {result.sources.map((source, index) => (
                  <li key={index} className="text-sm text-gray-600">
                    • {typeof source === 'string' ? source : source.content || '未知来源'}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}