import { useState, useRef, useEffect } from "react"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowUp, Paperclip, ChevronDown, Search } from "lucide-react"

interface SearchInputProps {
    value: string
    onChange: (value: string) => void
    onSubmit: () => void
    isLoading: boolean
    selectedSearchType: string
    onSearchTypeChange: (type: string) => void
    thinkActive: boolean
    onThinkToggle: () => void
}

const searchTypes = [
    { value: "深度搜索", label: "深度搜索" },
    { value: "更深度搜索", label: "更深度搜索" }
]

export const SearchInput = ({
    value,
    onChange,
    onSubmit,
    isLoading,
    selectedSearchType,
    onSearchTypeChange,
    thinkActive,
    onThinkToggle
}: SearchInputProps) => {
    const [dropdownOpen, setDropdownOpen] = useState(false)
    const dropdownRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            if (value.trim() && !isLoading) {
                onSubmit()
            }
        }
    }

    const handleTypeSelect = (type: string) => {
        onSearchTypeChange(type)
        setDropdownOpen(false)
    }

    return (
        <div className="relative">
            <Textarea
                className="p-3 sm:p-4 pb-12 sm:pb-12 block w-full border-gray-200 rounded-lg sm:text-sm focus:border-blue-500 focus:ring-blue-500 !border-none focus:!border-blue-500 focus-visible:!border-blue-500 min-h-[150px]"
                placeholder="你想知道什么？"
                style={{ minHeight: '150px' }}
                value={value}
                onChange={e => onChange(e.target.value)}
                onKeyDown={handleKeyDown}
            />

            {/* Bottom Toolbar */}
            <div className="absolute bottom-px inset-x-px p-2 rounded-b-lg bg-white flex items-center gap-1.5 border-2 border-transparent max-w-full text-[11px]">
                {/* 附件按钮 */}
                <Button
                    className="size-10 rounded-full text-gray-500 hover:bg-gray-100 flex items-center justify-center group/attach-button"
                    variant="ghost"
                    size="icon"
                    aria-label="附件"
                >
                    <Paperclip className="w-4 h-4 text-gray-600 group-hover/attach-button:text-gray-900 transition-colors duration-100" />
                </Button>

                {/* DeepSearch + Think 按钮组 */}
                <div className="flex grow gap-1.5 max-w-full">
                    <div className="grow flex gap-1.5 max-w-full">
                        {/* Search Dropdown */}
                        <div className="relative" ref={dropdownRef}>
                            <div className="flex ring-1 ring-gray-200 rounded-full items-center max-h-[40px] box-border transition-colors duration-100 relative overflow-hidden ring-inset bg-gray-100 hover:bg-gray-200">
                                <Button
                                    variant="ghost"
                                    className="h-10 px-3.5 py-2 text-sm rounded-full rounded-r-none pr-3 bg-transparent hover:bg-gray-200 flex items-center gap-2 group/ds-toggle focus-visible:ring-transparent"
                                    aria-label="DeepSearch"
                                >
                                    <Search className="w-4 h-4 text-gray-700 group-hover/ds-toggle:text-gray-900" />
                                    <span>{selectedSearchType}</span>
                                </Button>

                                <div className="h-4 w-[1px] bg-gray-300" />

                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-10 w-8 rounded-full rounded-l-none bg-transparent hover:bg-gray-200"
                                    aria-label="切换模式"
                                    onClick={() => setDropdownOpen(!dropdownOpen)}
                                >
                                    <ChevronDown className={`w-3 h-3 text-gray-600 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
                                </Button>
                            </div>

                            {/* Dropdown Menu */}
                            {dropdownOpen && (
                                <div className="absolute top-full mt-1 left-0 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50 min-w-[140px]">
                                    {searchTypes.map((type) => (
                                        <button
                                            key={type.value}
                                            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
                                            onClick={() => handleTypeSelect(type.value)}
                                        >
                                            <Search className="w-4 h-4 text-gray-600" />
                                            {type.label}
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Think Button */}
                        <Button
                            variant="ghost"
                            className="h-10 px-3.5 py-2 text-sm rounded-full border border-gray-200 group/think-toggle flex items-center gap-2 ml-1 transition-colors duration-100"
                            aria-label="Think"
                            onClick={onThinkToggle}
                        >
                            {thinkActive ? (
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="stroke-[2] group-hover/think-toggle:text-fg-primary text-fg-secondary">
                                    <path d="M19 9C19 12.866 15.866 17 12 17C8.13398 17 4.99997 12.866 4.99997 9C4.99997 5.13401 8.13398 3 12 3C15.866 3 19 5.13401 19 9Z" className="fill-yellow-100 dark:fill-yellow-300 origin-center transition-[transform,opacity] duration-100 scale-0 opacity-0" />
                                    <path d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z" fill="currentColor" />
                                    <path d="M9 16.0001H15" stroke="currentColor" />
                                    <path d="M12 16V12" stroke="currentColor" strokeLinecap="square" />
                                    <g>
                                        <path d="M20 7L19 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0" />
                                        <path d="M20 9L19 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0" />
                                        <path d="M4 7L5 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0" />
                                        <path d="M4 9L5 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0" />
                                    </g>
                                </svg>
                            ) : (
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="stroke-[2] text-primary">
                                    <path d="M19 9C19 12.866 15.866 17 12 17C8.13398 17 4.99997 12.866 4.99997 9C4.99997 5.13401 8.13398 3 12 3C15.866 3 19 5.13401 19 9Z" className="fill-yellow-100 dark:fill-yellow-300 origin-center transition-[transform,opacity] duration-100 scale-100 opacity-100" />
                                    <path d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z" fill="currentColor" />
                                    <path d="M9 16.0001H15" stroke="currentColor" />
                                    <path d="M12 16V12" stroke="currentColor" strokeLinecap="square" />
                                    <g>
                                        <path d="M20 7L19 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-[3px] -translate-y-[3px] opacity-100" />
                                        <path d="M20 9L19 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out translate-x-[3px] translate-y-[3px] opacity-100" />
                                        <path d="M4 7L5 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out -translate-x-[3px] -translate-y-[3px] opacity-100" />
                                        <path d="M4 9L5 8" stroke="currentColor" strokeLinecap="round" className="transition-[transform,opacity] duration-100 ease-in-out -translate-x-[3px] translate-y-[3px] opacity-100" />
                                    </g>
                                </svg>
                            )}
                            <span>思考</span>
                        </Button>
                    </div>
                </div>

                {/* 右侧语音和发送按钮 */}
                <div className="flex items-center gap-x-1 ml-auto">
                    <Button
                        className="size-8 rounded-lg text-gray-500 hover:bg-gray-100 flex items-center justify-center"
                        variant="ghost"
                        size="icon"
                        aria-label="语音"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                            <line x1="12" x2="12" y1="19" y2="22" />
                        </svg>
                    </Button>

                    <Button
                        className={`size-8 rounded-lg flex items-center justify-center ${value && !isLoading
                                ? 'text-white bg-slate-700 hover:bg-slate-500'
                                : 'text-gray-400 bg-gray-200 cursor-not-allowed'
                            }`}
                        variant="default"
                        size="icon"
                        aria-label="发送"
                        disabled={!value || isLoading}
                        onClick={onSubmit}
                    >
                        {isLoading ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        ) : (
                            <ArrowUp className="w-4 h-4" />
                        )}
                    </Button>
                </div>
            </div>
        </div>
    )
}