import { useState, useEffect, useRef, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Eye, Loader2, AlertCircle, Globe, FileText, ExternalLink, LayoutPanelLeft, LayoutPanelTop, ChevronDown, ChevronUp } from 'lucide-react'
// pdfjs正确导入方式（兼容Vite环境）
import * as pdfjsLib from 'pdfjs-dist'
import 'pdfjs-dist/web/pdf_viewer.css'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface DocumentForm {
    type: 'file' | 'url' | 'text'
    file?: File
    url?: string
    content?: string
}

interface DocumentPreviewProps {
    form: DocumentForm
}

// Helper functions - you'll need to import these from your utils or define them here
const getFileType = (file: File): string => {
    const extension = file.name.split('.').pop()?.toLowerCase()

    if (['txt', 'md', 'markdown'].includes(extension || '')) return 'text'
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) return 'image'
    if (extension === 'pdf') return 'pdf'
    if (['doc', 'docx'].includes(extension || '')) return 'document'
    if (['xls', 'xlsx'].includes(extension || '')) return 'spreadsheet'

    return 'other'
}

const getFileIcon = (fileType: string) => {
    // Return appropriate icon based on file type
    return <FileText className="w-8 h-8 text-blue-500" />
}

const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function DocumentPreview({ form }: DocumentPreviewProps) {
    const [previewContent, setPreviewContent] = useState<string>('')
    const [previewLoading, setPreviewLoading] = useState(false)
    const [previewError, setPreviewError] = useState<string>('')
    const [useIframe, setUseIframe] = useState(false)
    const [isWeChatArticle, setIsWeChatArticle] = useState(false)
    const pdfCanvasRef = useRef<HTMLCanvasElement | null>(null)
    const pdfCanvasLeftRef = useRef<HTMLCanvasElement | null>(null)
    const pdfCanvasRightRef = useRef<HTMLCanvasElement | null>(null)
    const [pdfNumPages, setPdfNumPages] = useState<number>(0)
    const [pdfPage, setPdfPage] = useState<number>(1)
    const [pdfDoc, setPdfDoc] = useState<any>(null)
    const [isDoublePage, setIsDoublePage] = useState(true)
    
    // 懒加载相关状态
    const [displayedContent, setDisplayedContent] = useState<string>('')
    const [isLoadingMore, setIsLoadingMore] = useState(false)
    const [hasMoreContent, setHasMoreContent] = useState(false)
    const [currentChunk, setCurrentChunk] = useState(0)
    const [contentChunks, setContentChunks] = useState<string[]>([])
    const CHUNK_SIZE = 3000 // 每个块大约3000字符
    const INITIAL_CHUNKS = 2 // 初始显示2个块

    useEffect(() => {
        if (form.type === 'url' && form.url) {
            setPreviewLoading(true)
            setPreviewError('')
            setUseIframe(false)

            // 检查URL是否可以用iframe预览
            const canUseIframe = (url: string) => {
                try {
                    const urlObj = new URL(url)
                    // 一些常见的支持iframe的网站
                    const iframeFriendly = [
                        'github.com',
                        'stackoverflow.com',
                        'medium.com',
                        'dev.to',
                        'wikipedia.org'
                    ]

                    // 微信公众号文章不支持iframe嵌入
                    const iframeBlocked = [
                        'mp.weixin.qq.com'
                    ]

                    if (iframeBlocked.some(domain => urlObj.hostname.includes(domain))) {
                        return false
                    }

                    return iframeFriendly.some(domain => urlObj.hostname.includes(domain))
                } catch {
                    return false
                }
            }

            // 检查是否为微信公众号文章
            const checkIsWeChatArticle = (url: string) => {
                try {
                    const urlObj = new URL(url)
                    return urlObj.hostname.includes('mp.weixin.qq.com')
                } catch {
                    return false
                }
            }

            const weChatArticle = checkIsWeChatArticle(form.url)
            setIsWeChatArticle(weChatArticle)

            if (canUseIframe(form.url)) {
                setUseIframe(true)
                setPreviewLoading(false)
            } else {
                // 模拟URL预览加载
                setTimeout(() => {
                    if (weChatArticle) {
                        setPreviewContent(`📱 微信公众号文章
                        
由于微信的安全策略，公众号文章无法直接嵌入预览。

🔗 原文链接：${form.url}

💡 建议处理方式：
• 点击"在新窗口打开"按钮查看完整文章
• 或复制文章内容到文本框进行处理
• 系统将通过后端服务提取文章内容进行分析

⚠️ 注意：微信公众号文章受版权保护，请遵守相关使用规定。`)
                    } else if (form.url?.includes('pdf')) {
                        setPreviewContent('PDF文档预览：这是一个PDF文档的预览内容...')
                    } else if (form.url?.includes('github') || form.url?.includes('.md')) {
                        setPreviewContent('# Markdown文档\n\n这是一个Markdown文档的预览内容...\n\n## 章节标题\n\n内容详情...')
                    } else {
                        setPreviewContent('网页内容预览：这是网页的主要内容摘要...')
                    }
                    setPreviewLoading(false)
                }, 1000)
            }
        } else if (form.type === 'file' && form.file) {
            const fileType = getFileType(form.file)
            setPreviewLoading(true)

            if (fileType === 'text' || fileType === 'markdown') {
                const reader = new FileReader()
                reader.onload = (e) => {
                    setPreviewContent(e.target?.result as string || '')
                    setPreviewLoading(false)
                }
                reader.onerror = () => {
                    setPreviewError('文件读取失败')
                    setPreviewLoading(false)
                }
                reader.readAsText(form.file)
            } else if (fileType === 'image') {
                const reader = new FileReader()
                reader.onload = (e) => {
                    setPreviewContent(e.target?.result as string || '')
                    setPreviewLoading(false)
                }
                reader.readAsDataURL(form.file)
            } else if (fileType === 'pdf') {
                setPreviewLoading(true)
                setPreviewError('')
                setPdfDoc(null)
                setPdfPage(1)
                const reader = new FileReader()
                reader.onload = async (e) => {
                    try {
                        const typedarray = new Uint8Array(e.target?.result as ArrayBuffer)
                        // pdfjs worker 路径
                        pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
                        const loadingTask = pdfjsLib.getDocument({ data: typedarray })
                        const pdf = await loadingTask.promise
                        setPdfDoc(pdf)
                        setPdfNumPages(pdf.numPages)
                        setPreviewLoading(false)
                    } catch (err) {
                        setPreviewError('PDF文件解析失败')
                        setPreviewLoading(false)
                    }
                }
                reader.onerror = () => {
                    setPreviewError('文件读取失败')
                    setPreviewLoading(false)
                }
                reader.readAsArrayBuffer(form.file)
            } else {
                setTimeout(() => {
                    setPreviewContent(`${fileType.toUpperCase()}文件预览：${form.file?.name}`)
                    setPreviewLoading(false)
                }, 500)
            }
        } else if (form.type === 'text' && form.content) {
            setPreviewContent(form.content)
        }
    }, [form.type, form.url, form.file, form.content])

    // 内容分块处理
    useEffect(() => {
        if (previewContent) {
            // 如果内容小于初始块大小，直接显示全部
            if (previewContent.length <= CHUNK_SIZE * INITIAL_CHUNKS) {
                setDisplayedContent(previewContent)
                setContentChunks([previewContent])
                setCurrentChunk(1)
                setHasMoreContent(false)
            } else {
                const chunks = splitContentIntoChunks(previewContent, CHUNK_SIZE)
                setContentChunks(chunks)
                setCurrentChunk(Math.min(INITIAL_CHUNKS, chunks.length))
                setHasMoreContent(chunks.length > INITIAL_CHUNKS)
                setDisplayedContent(chunks.slice(0, INITIAL_CHUNKS).join(''))
            }
        }
    }, [previewContent])

    // 分块函数 - 智能分块，避免在Markdown语法中间断开
    const splitContentIntoChunks = (content: string, chunkSize: number): string[] => {
        const chunks: string[] = []
        let currentChunk = ''
        const lines = content.split('\n')
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i]
            const nextLine = lines[i + 1] || ''
            
            // 检查是否应该在这里分块
            if ((currentChunk + line).length > chunkSize && currentChunk) {
                // 尝试在合适的位置分块
                let splitPoint = currentChunk.length
                
                // 优先在段落之间分块
                const lastParagraphEnd = currentChunk.lastIndexOf('\n\n')
                if (lastParagraphEnd > chunkSize * 0.7) {
                    splitPoint = lastParagraphEnd + 2
                } else {
                    // 其次在句子之间分块
                    const lastSentenceEnd = currentChunk.lastIndexOf('. ')
                    if (lastSentenceEnd > chunkSize * 0.8) {
                        splitPoint = lastSentenceEnd + 2
                    } else {
                        // 最后在行之间分块
                        const lastLineEnd = currentChunk.lastIndexOf('\n')
                        if (lastLineEnd > chunkSize * 0.9) {
                            splitPoint = lastLineEnd + 1
                        }
                    }
                }
                
                chunks.push(currentChunk.substring(0, splitPoint).trim())
                currentChunk = currentChunk.substring(splitPoint) + line + '\n'
            } else {
                currentChunk += line + '\n'
            }
        }
        
        if (currentChunk.trim()) {
            chunks.push(currentChunk.trim())
        }
        
        return chunks
    }

    // 加载更多内容
    const loadMoreContent = useCallback(() => {
        if (isLoadingMore || !hasMoreContent) return
        
        setIsLoadingMore(true)
        
        // 模拟加载延迟，实际项目中可以移除
        setTimeout(() => {
            const nextChunk = currentChunk + 1
            const newContent = contentChunks.slice(0, nextChunk).join('')
            setDisplayedContent(newContent)
            setCurrentChunk(nextChunk)
            setHasMoreContent(nextChunk < contentChunks.length)
            setIsLoadingMore(false)
        }, 300)
    }, [currentChunk, contentChunks, hasMoreContent, isLoadingMore])

    useEffect(() => {
        if (form.type === 'file' && form.file && getFileType(form.file) === 'pdf') {
            setPreviewLoading(true)
            setPreviewError('')
            setPdfDoc(null)
            setPdfPage(1)
            const reader = new FileReader()
            reader.onload = async (e) => {
                try {
                    const typedarray = new Uint8Array(e.target?.result as ArrayBuffer)
                    // pdfjs worker 路径
                    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
                    const loadingTask = pdfjsLib.getDocument({ data: typedarray })
                    const pdf = await loadingTask.promise
                    setPdfDoc(pdf)
                    setPdfNumPages(pdf.numPages)
                    setPreviewLoading(false)
                } catch (err) {
                    setPreviewError('PDF文件解析失败')
                    setPreviewLoading(false)
                }
            }
            reader.onerror = () => {
                setPreviewError('文件读取失败')
                setPreviewLoading(false)
            }
            reader.readAsArrayBuffer(form.file)
        }
    }, [form.type, form.file])

    // 替换双页渲染useEffect为根据isDoublePage动态渲染
    useEffect(() => {
        if (!pdfDoc) return
        const render = async () => {
            // 渲染左页/单页
            if (pdfCanvasLeftRef.current) {
                const page = await pdfDoc.getPage(pdfPage)
                const canvas = pdfCanvasLeftRef.current
                if (!canvas) return
                const parent = canvas.parentElement
                const parentWidth = parent ? parent.clientWidth : canvas.offsetWidth
                const viewport = page.getViewport({ scale: parentWidth / page.view[2] })
                const context = canvas.getContext('2d')
                if (!context) return
                canvas.width = viewport.width
                canvas.height = viewport.height
                await page.render({ canvasContext: context, viewport }).promise
            }
            // 渲染右页（仅双页模式）
            if (isDoublePage && pdfCanvasRightRef.current) {
                const rightPageNum = pdfPage + 1
                if (rightPageNum <= pdfNumPages) {
                    const page = await pdfDoc.getPage(rightPageNum)
                    const canvas = pdfCanvasRightRef.current
                    if (!canvas) return
                    const parent = canvas.parentElement
                    const parentWidth = parent ? parent.clientWidth : canvas.offsetWidth
                    const viewport = page.getViewport({ scale: parentWidth / page.view[2] })
                    const context = canvas.getContext('2d')
                    if (!context) return
                    canvas.width = viewport.width
                    canvas.height = viewport.height
                    await page.render({ canvasContext: context, viewport }).promise
                } else {
                    // 清空右页canvas
                    const canvas = pdfCanvasRightRef.current
                    if (!canvas) return
                    const context = canvas.getContext('2d')
                    if (context) context.clearRect(0, 0, canvas.width, canvas.height)
                }
            } else if (pdfCanvasRightRef.current) {
                // 单页模式下清空右页canvas
                const canvas = pdfCanvasRightRef.current
                if (!canvas) return
                const context = canvas.getContext('2d')
                if (context) context.clearRect(0, 0, canvas.width, canvas.height)
            }
        }
        render()
    }, [pdfDoc, pdfPage, pdfNumPages, isDoublePage])

    if (!form.url && !form.file && !form.content) {
        return (
            <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <div className="text-center">
                    <Eye className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">选择文档后将显示预览</p>
                </div>
            </div>
        )
    }

    if (previewLoading) {
        return (
            <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
                <div className="text-center">
                    <Loader2 className="w-8 h-8 text-purple-500 animate-spin mx-auto mb-2" />
                    <p className="text-gray-600">正在加载预览...</p>
                </div>
            </div>
        )
    }

    if (previewError) {
        return (
            <div className="flex items-center justify-center h-48 bg-red-50 rounded-lg border border-red-200">
                <div className="text-center">
                    <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
                    <p className="text-red-600">{previewError}</p>
                </div>
            </div>
        )
    }

    const fileType = form.file ? getFileType(form.file) : 'other'

    return (
        <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden flex-1">
            <div className="p-4 bg-white border-b border-gray-200" />
            <div className={`p-4 flex-1 h-full min-h-[24rem] flex flex-col justify-center items-center overflow-auto w-full`}>
                {useIframe && form.url ? (
                    <div className="relative w-full h-full bg-white rounded border">
                        <iframe
                            src={form.url}
                            className="w-full h-full rounded border-0"
                            title="网页预览"
                            sandbox="allow-scripts allow-same-origin"
                            onError={() => {
                                setUseIframe(false)
                                setPreviewContent('无法加载网页预览，可能由于安全限制')
                            }}
                        />
                        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                            实时预览
                        </div>
                    </div>
                ) : fileType === 'image' && form.file ? (
                    <img
                        src={previewContent}
                        alt="预览"
                        className="max-w-full h-auto rounded-lg shadow-sm"
                    />
                ) : fileType === 'pdf' && form.file ? (
                    <div className="flex flex-col items-center gap-2 w-full h-full flex-1 justify-center relative">
                        <div className="absolute top-4 right-6 z-10">
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            type="button"
                                            size="icon"
                                            variant="ghost"
                                            onClick={() => setIsDoublePage(v => !v)}
                                            className="shadow rounded-full p-2 hover:bg-gray-200"
                                            aria-label={isDoublePage ? '切换为单页视图' : '切换为双页视图'}
                                        >
                                            {isDoublePage ? (
                                                <LayoutPanelLeft className="w-5 h-5" />
                                            ) : (
                                                <LayoutPanelTop className="w-5 h-5" />
                                            )}
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent side="left">
                                        {isDoublePage ? '切换为单页视图' : '切换为双页视图'}
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                        {isDoublePage ? (
                            <div className="flex flex-row justify-center items-center w-full h-full gap-4">
                                <canvas
                                    ref={pdfCanvasLeftRef}
                                    className="rounded border shadow object-contain bg-white flex-1"
                                    style={{ height: '70vh', maxHeight: '80vh', display: 'block', minWidth: 0 }}
                                />
                                {pdfPage + 1 <= pdfNumPages && (
                                    <canvas
                                        ref={pdfCanvasRightRef}
                                        className="rounded border shadow object-contain bg-white flex-1"
                                        style={{ height: '70vh', maxHeight: '80vh', display: 'block', minWidth: 0 }}
                                    />
                                )}
                            </div>
                        ) : (
                            <div className="flex flex-row justify-center items-center w-full h-full">
                                <canvas
                                    ref={pdfCanvasLeftRef}
                                    className="rounded border shadow object-contain bg-white flex-1"
                                    style={{ height: '70vh', maxHeight: '80vh', display: 'block', minWidth: 0 }}
                                />
                            </div>
                        )}
                        <div className="flex gap-2 items-center mt-2">
                            <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => setPdfPage(p => isDoublePage ? Math.max(1, p - 2) : Math.max(1, p - 1))}
                                disabled={pdfPage <= 1}
                            >{isDoublePage ? '上一组' : '上一页'}</Button>
                            <span className="text-xs text-gray-600">{pdfPage} / {pdfNumPages}</span>
                            <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => setPdfPage(p => isDoublePage ? Math.min(pdfNumPages - (pdfNumPages % 2 === 0 ? 1 : 0), p + 2) : Math.min(pdfNumPages, p + 1))}
                                disabled={pdfPage >= pdfNumPages}
                            >{isDoublePage ? '下一组' : '下一页'}</Button>
                        </div>
                    </div>
                ) : fileType === 'text' && (form.file?.name.endsWith('.md') || form.file?.name.endsWith('.markdown') || form.content?.includes('#')) ? (
                    // Markdown渲染
                    <div className="w-full h-full overflow-auto bg-white p-6 rounded border">
                        <div className="markdown-content text-sm leading-relaxed">
                            <ReactMarkdown 
                                remarkPlugins={[remarkGfm]}
                                components={{
                                    // 标题样式
                                    h1: ({ children }: any) => (
                                        <h1 className="text-2xl font-bold text-gray-900 mb-4 mt-6 first:mt-0">
                                            {children}
                                        </h1>
                                    ),
                                    h2: ({ children }: any) => (
                                        <h2 className="text-xl font-bold text-gray-900 mb-3 mt-5 first:mt-0">
                                            {children}
                                        </h2>
                                    ),
                                    h3: ({ children }: any) => (
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2 mt-4 first:mt-0">
                                            {children}
                                        </h3>
                                    ),
                                    h4: ({ children }: any) => (
                                        <h4 className="text-base font-semibold text-gray-900 mb-2 mt-3 first:mt-0">
                                            {children}
                                        </h4>
                                    ),
                                    // 段落样式
                                    p: ({ children }: any) => (
                                        <p className="mb-4 text-gray-700 last:mb-0">
                                            {children}
                                        </p>
                                    ),
                                    // 列表样式
                                    ul: ({ children }: any) => (
                                        <ul className="mb-4 list-disc list-inside text-gray-700 space-y-1">
                                            {children}
                                        </ul>
                                    ),
                                    ol: ({ children }: any) => (
                                        <ol className="mb-4 list-decimal list-inside text-gray-700 space-y-1">
                                            {children}
                                        </ol>
                                    ),
                                    li: ({ children }: any) => (
                                        <li className="text-gray-700">
                                            {children}
                                        </li>
                                    ),
                                    // 链接样式
                                    a: ({ href, children }: any) => (
                                        <a 
                                            href={href} 
                                            className="text-blue-600 hover:text-blue-800 underline"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {children}
                                        </a>
                                    ),
                                    // 代码块样式
                                    code: ({ className, children, ...props }: any) => {
                                        const match = /language-(\w+)/.exec(className || '')
                                        const isInline = !match
                                        return !isInline ? (
                                            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto mb-4 border">
                                                <code className={className} {...props}>
                                                    {children}
                                                </code>
                                            </pre>
                                        ) : (
                                            <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" {...props}>
                                                {children}
                                            </code>
                                        )
                                    },
                                    // 表格样式
                                    table: ({ children }: any) => (
                                        <div className="overflow-x-auto mb-4">
                                            <table className="min-w-full border-collapse border border-gray-300">
                                                {children}
                                            </table>
                                        </div>
                                    ),
                                    th: ({ children }: any) => (
                                        <th className="border border-gray-300 px-3 py-2 bg-gray-50 font-medium text-left">
                                            {children}
                                        </th>
                                    ),
                                    td: ({ children }: any) => (
                                        <td className="border border-gray-300 px-3 py-2 text-gray-700">
                                            {children}
                                        </td>
                                    ),
                                    // 引用样式
                                    blockquote: ({ children }: any) => (
                                        <blockquote className="border-l-4 border-gray-300 pl-4 py-2 mb-4 bg-gray-50 italic text-gray-700">
                                            {children}
                                        </blockquote>
                                    ),
                                    // 水平线样式
                                    hr: () => (
                                        <hr className="border-gray-300 my-6" />
                                    ),
                                    // 强调样式
                                    strong: ({ children }: any) => (
                                        <strong className="font-semibold text-gray-900">
                                            {children}
                                        </strong>
                                    ),
                                    em: ({ children }: any) => (
                                        <em className="italic text-gray-700">
                                            {children}
                                        </em>
                                    ),
                                }}
                            >
                                {displayedContent}
                            </ReactMarkdown>
                            
                            {/* 懒加载更多按钮 */}
                            {hasMoreContent && (
                                <div className="flex justify-center mt-6 pb-4">
                                    <Button
                                        onClick={loadMoreContent}
                                        disabled={isLoadingMore}
                                        variant="outline"
                                        className="flex items-center gap-2"
                                    >
                                        {isLoadingMore ? (
                                            <>
                                                <Loader2 className="w-4 h-4 animate-spin" />
                                                加载中...
                                            </>
                                        ) : (
                                            <>
                                                <ChevronDown className="w-4 h-4" />
                                                加载更多内容
                                            </>
                                        )}
                                    </Button>
                                </div>
                            )}
                            
                            {/* 内容统计信息 */}
                            {contentChunks.length > 0 && (
                                <div className="text-xs text-gray-500 text-center mt-4 pb-2">
                                    已显示 {currentChunk} / {contentChunks.length} 块内容
                                    {contentChunks.length > 1 && (
                                        <span className="ml-2">
                                            (约 {Math.round((currentChunk / contentChunks.length) * 100)}%)
                                        </span>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono bg-white p-3 rounded border">
                        {previewContent.length > 500
                            ? previewContent.substring(0, 500) + '...'
                            : previewContent
                        }
                    </pre>
                )}
            </div>
        </div>
    )
}