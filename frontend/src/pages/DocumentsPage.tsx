import { useState, useEffect } from "react"
import {
    Search, Plus, Settings, Globe, Clock, Layers, RefreshCw, AlertCircle,
    CheckCircle, Pause, MoreHorizontal, Eye, Edit3, BookOpen,
    User, Building2, FileText, Link, GraduationCap, Target, Network,
    ChevronRight, ChevronDown, ChevronLeft, Folder, FolderOpen, Home
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { DocumentList } from "@/components/DocumentList"
import { AddDocumentDialog } from "@/components/AddDocumentDialog"

// 文档类型定义
interface DocumentItem {
    id: string
    name: string
    type: 'business' | 'personal'
    category: string
    subCategory: string
    priority: 'high' | 'medium' | 'low'
    status: 'active' | 'inactive' | 'processing'
    lastUpdated: string
    size: string
    compression: number
    relevanceScore: number
    tags: string[]
    description: string
    source: string
    knowledgeGraph?: boolean
    metadata?: any
}

// API 配置
const API_BASE_URL = 'http://localhost:8000'

// API 调用函数
const fetchDocuments = async (params: {
    type?: 'business' | 'personal' | 'all'
    category?: string
    status?: 'active' | 'inactive' | 'processing'
    priority?: 'high' | 'medium' | 'low'
    search?: string
    limit?: number
    offset?: number
}) => {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
            searchParams.append(key, String(value))
        }
    })

    const response = await fetch(`${API_BASE_URL}/api/documents?${searchParams}`)
    if (!response.ok) {
        throw new Error('Failed to fetch documents')
    }
    return response.json()
}

// 导航树节点类型
interface NavTreeNode {
    id: string
    name: string
    type: 'root' | 'type' | 'category' | 'subcategory'
    children?: NavTreeNode[]
    count?: number
    expanded?: boolean
    path: string[]
}

export default function DocumentsPage() {
    const [searchTerm, setSearchTerm] = useState("")
    const [activeTab, setActiveTab] = useState<'business' | 'personal' | 'all'>('all')
    const [selectedCategory, setSelectedCategory] = useState<string>('all')
    const [selectedSubCategory, setSelectedSubCategory] = useState<string>('all')
    const [documents, setDocuments] = useState<DocumentItem[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['business', 'personal']))
    const [showAddDialog, setShowAddDialog] = useState(false)

    // 加载文档数据
    const loadDocuments = async () => {
        try {
            setLoading(true)
            setError(null)
            const data = await fetchDocuments({
                type: activeTab,
                category: selectedCategory === 'all' ? undefined : selectedCategory,
                search: searchTerm || undefined,
                limit: 50
            })
            setDocuments(data)
        } catch (err) {
            setError(err instanceof Error ? err.message : '加载文档失败')
        } finally {
            setLoading(false)
        }
    }

    // 初始加载和依赖更新
    useEffect(() => {
        loadDocuments()
    }, [activeTab, selectedCategory, selectedSubCategory, searchTerm])

    // 过滤后的文档 - 根据当前选择的导航节点进行精确过滤
    const filteredDocuments = documents.filter(doc => {
        // 类型过滤
        if (activeTab !== 'all' && doc.type !== activeTab) {
            return false
        }

        // 类别过滤
        if (selectedCategory !== 'all' && doc.category !== selectedCategory) {
            return false
        }

        // 子类别过滤
        if (selectedSubCategory !== 'all' && doc.subCategory !== selectedSubCategory) {
            return false
        }

        // 搜索过滤
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase()
            return (
                doc.name.toLowerCase().includes(searchLower) ||
                doc.description.toLowerCase().includes(searchLower) ||
                doc.category.toLowerCase().includes(searchLower) ||
                doc.subCategory.toLowerCase().includes(searchLower) ||
                doc.tags.some(tag => tag.toLowerCase().includes(searchLower))
            )
        }

        return true
    })

    // 获取状态图标
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />
            case 'processing': return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
            case 'inactive': return <Pause className="w-4 h-4 text-gray-400" />
            default: return <AlertCircle className="w-4 h-4 text-yellow-500" />
        }
    }

    // 获取优先级颜色
    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return 'bg-red-100 text-red-700 border-red-200'
            case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
            case 'low': return 'bg-green-100 text-green-700 border-green-200'
            default: return 'bg-gray-100 text-gray-700 border-gray-200'
        }
    }

    // 获取文档类型图标
    const getDocumentTypeIcon = (type: string, category: string) => {
        if (type === 'business') {
            switch (category) {
                case '研发设计': return <FileText className="w-4 h-4 text-blue-600" />
                case '生产管控': return <Building2 className="w-4 h-4 text-green-600" />
                case '运维数据': return <Settings className="w-4 h-4 text-orange-600" />
                default: return <Building2 className="w-4 h-4 text-blue-600" />
            }
        } else {
            switch (category) {
                case '规范标准': return <BookOpen className="w-4 h-4 text-purple-600" />
                case '学术论文': return <GraduationCap className="w-4 h-4 text-indigo-600" />
                case '网络资源': return <Link className="w-4 h-4 text-teal-600" />
                default: return <User className="w-4 h-4 text-purple-600" />
            }
        }
    }

    // 获取所有类别
    const getAllCategories = () => {
        const categories = new Set(documents.map(doc => doc.category))
        return Array.from(categories)
    }

    // 构建导航树
    const buildNavTree = (): NavTreeNode[] => {
        const tree: NavTreeNode[] = [
            {
                id: 'all',
                name: '全部文档',
                type: 'root',
                path: ['all'],
                count: documents.length
            }
        ]

        // 按类型分组
        const businessDocs = documents.filter(doc => doc.type === 'business')
        const personalDocs = documents.filter(doc => doc.type === 'personal')

        if (businessDocs.length > 0) {
            const businessNode: NavTreeNode = {
                id: 'business',
                name: '业务文档',
                type: 'type',
                path: ['business'],
                count: businessDocs.length,
                children: []
            }

            // 按类别分组业务文档
            const businessCategories = new Map<string, DocumentItem[]>()
            businessDocs.forEach(doc => {
                if (!businessCategories.has(doc.category)) {
                    businessCategories.set(doc.category, [])
                }
                businessCategories.get(doc.category)!.push(doc)
            })

            businessCategories.forEach((docs, category) => {
                const categoryNode: NavTreeNode = {
                    id: `business-${category}`,
                    name: category,
                    type: 'category',
                    path: ['business', category],
                    count: docs.length,
                    children: []
                }

                // 按子类别分组
                const subCategories = new Map<string, DocumentItem[]>()
                docs.forEach(doc => {
                    if (!subCategories.has(doc.subCategory)) {
                        subCategories.set(doc.subCategory, [])
                    }
                    subCategories.get(doc.subCategory)!.push(doc)
                })

                subCategories.forEach((subDocs, subCategory) => {
                    categoryNode.children!.push({
                        id: `business-${category}-${subCategory}`,
                        name: subCategory,
                        type: 'subcategory',
                        path: ['business', category, subCategory],
                        count: subDocs.length
                    })
                })

                businessNode.children!.push(categoryNode)
            })

            tree.push(businessNode)
        }

        if (personalDocs.length > 0) {
            const personalNode: NavTreeNode = {
                id: 'personal',
                name: '个人知识库',
                type: 'type',
                path: ['personal'],
                count: personalDocs.length,
                children: []
            }

            // 按类别分组个人文档
            const personalCategories = new Map<string, DocumentItem[]>()
            personalDocs.forEach(doc => {
                if (!personalCategories.has(doc.category)) {
                    personalCategories.set(doc.category, [])
                }
                personalCategories.get(doc.category)!.push(doc)
            })

            personalCategories.forEach((docs, category) => {
                const categoryNode: NavTreeNode = {
                    id: `personal-${category}`,
                    name: category,
                    type: 'category',
                    path: ['personal', category],
                    count: docs.length,
                    children: []
                }

                // 按子类别分组
                const subCategories = new Map<string, DocumentItem[]>()
                docs.forEach(doc => {
                    if (!subCategories.has(doc.subCategory)) {
                        subCategories.set(doc.subCategory, [])
                    }
                    subCategories.get(doc.subCategory)!.push(doc)
                })

                subCategories.forEach((subDocs, subCategory) => {
                    categoryNode.children!.push({
                        id: `personal-${category}-${subCategory}`,
                        name: subCategory,
                        type: 'subcategory',
                        path: ['personal', category, subCategory],
                        count: subDocs.length
                    })
                })

                personalNode.children!.push(categoryNode)
            })

            tree.push(personalNode)
        }

        return tree
    }

    // 切换节点展开状态
    const toggleNodeExpansion = (nodeId: string) => {
        setExpandedNodes(prev => {
            const newSet = new Set(prev)
            if (newSet.has(nodeId)) {
                newSet.delete(nodeId)
            } else {
                newSet.add(nodeId)
            }
            return newSet
        })
    }

    // 处理导航选择
    const handleNavSelection = (node: NavTreeNode) => {
        const [type, category, subCategory] = node.path

        if (type === 'all') {
            setActiveTab('all')
            setSelectedCategory('all')
            setSelectedSubCategory('all')
        } else if (type === 'business' || type === 'personal') {
            setActiveTab(type as 'business' | 'personal')

            // 根据节点类型设置精确的筛选条件
            if (node.type === 'type') {
                // 点击类型节点，显示该类型下的所有文档
                setSelectedCategory('all')
                setSelectedSubCategory('all')
            } else if (node.type === 'category') {
                // 点击类别节点，显示该类别下的所有文档
                setSelectedCategory(category || 'all')
                setSelectedSubCategory('all')
            } else if (node.type === 'subcategory') {
                // 点击子类别节点，只显示该子类别的文档
                setSelectedCategory(category || 'all')
                setSelectedSubCategory(subCategory || 'all')
            }
        }
    }

    // 渲染导航树节点
    const renderNavNode = (node: NavTreeNode, level: number = 0) => {
        const isExpanded = expandedNodes.has(node.id)
        const hasChildren = node.children && node.children.length > 0
        const isSelected = (() => {
            // 全部文档节点
            if (node.type === 'root' && node.path[0] === 'all') {
                return activeTab === 'all' && selectedCategory === 'all' && selectedSubCategory === 'all'
            }

            // 类型节点（业务文档/个人知识库）
            if (node.type === 'type') {
                return activeTab === node.path[0] && selectedCategory === 'all' && selectedSubCategory === 'all'
            }

            // 类别节点
            if (node.type === 'category') {
                return activeTab === node.path[0] && selectedCategory === node.path[1] && selectedSubCategory === 'all'
            }

            // 子类别节点
            if (node.type === 'subcategory') {
                return activeTab === node.path[0] && selectedCategory === node.path[1] && selectedSubCategory === node.path[2]
            }

            return false
        })()

        return (
            <div key={node.id}>
                <div
                    className={`flex items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded-lg ${isSelected ? 'bg-purple-100 text-purple-700 font-medium' : 'text-gray-700'
                        }`}
                    style={{ paddingLeft: `${12 + level * 16}px` }}
                    onClick={() => {
                        if (hasChildren) {
                            toggleNodeExpansion(node.id)
                        }
                        handleNavSelection(node)
                    }}
                >
                    {hasChildren && (
                        <button className="p-0.5 hover:bg-gray-200 rounded">
                            {isExpanded ? (
                                <ChevronDown className="w-3 h-3" />
                            ) : (
                                <ChevronRight className="w-3 h-3" />
                            )}
                        </button>
                    )}
                    {!hasChildren && <div className="w-4" />}

                    {node.type === 'root' && <Home className="w-4 h-4" />}
                    {node.type === 'type' && (
                        node.id === 'business' ?
                            <Building2 className="w-4 h-4 text-blue-600" /> :
                            <User className="w-4 h-4 text-green-600" />
                    )}
                    {node.type === 'category' && (
                        isExpanded ? <FolderOpen className="w-4 h-4 text-orange-500" /> : <Folder className="w-4 h-4 text-orange-500" />
                    )}
                    {node.type === 'subcategory' && getDocumentTypeIcon(
                        node.path[0] as 'business' | 'personal',
                        node.path[1]
                    )}

                    <span className="flex-1">{node.name}</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                        {node.count}
                    </span>
                </div>

                {hasChildren && isExpanded && (
                    <div>
                        {node.children!.map(child => renderNavNode(child, level + 1))}
                    </div>
                )}
            </div>
        )
    }

    // 生成面包屑导航
    const getBreadcrumbs = () => {
        const breadcrumbs = []

        if (activeTab === 'all') {
            breadcrumbs.push({ name: '全部文档', path: ['all'] })
        } else {
            breadcrumbs.push({
                name: activeTab === 'business' ? '业务文档' : '个人知识库',
                path: [activeTab]
            })

            if (selectedCategory !== 'all') {
                breadcrumbs.push({
                    name: selectedCategory,
                    path: [activeTab, selectedCategory]
                })

                if (selectedSubCategory !== 'all') {
                    breadcrumbs.push({
                        name: selectedSubCategory,
                        path: [activeTab, selectedCategory, selectedSubCategory]
                    })
                }
            }
        }

        return breadcrumbs
    }

    // 处理添加文档
    const handleDocumentAdd = (newDocument: Omit<DocumentItem, 'id'>) => {
        const documentWithId = {
            ...newDocument,
            id: Date.now().toString() // 临时ID生成方式
        }
        setDocuments(prev => [documentWithId, ...prev])
        setShowAddDialog(false)
    }
    return (
        <div className="h-screen bg-gray-50 flex flex-col">
            {/* 主内容区 */}
            <div className="flex-1 flex">
                {/* 左侧内容区 */}
                <div className="flex-1 flex flex-col">
                    {/* 页面头部 */}
                    <div className="bg-white border-b border-gray-200 px-6 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <img src="/spider-web.svg" alt="Spider Web" className="w-8 h-8" />
                                <div>
                                    <h1 className="text-2xl font-semibold text-gray-900">上下文知识</h1>
                                    <p className="text-sm text-gray-600">业务文档与个人知识图谱管理</p>
                                </div>
                            </div>
                                                {/* 统计面板 */}
                    <div className="bg-white border-b border-gray-200 px-6 py-4">
                        <div className="grid grid-cols-4 gap-6">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-100 rounded-lg">
                                    <Building2 className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <div className="text-sm text-gray-600">业务文档</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                        {documents.filter(doc => doc.type === 'business').length}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-100 rounded-lg">
                                    <User className="w-5 h-5 text-green-600" />
                                </div>
                                <div>
                                    <div className="text-sm text-gray-600">个人知识库</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                        {documents.filter(doc => doc.type === 'personal').length}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <Network className="w-5 h-5 text-purple-600" />
                                </div>
                                <div>
                                    <div className="text-sm text-gray-600">知识图谱节点</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                        {documents.filter(doc => doc.knowledgeGraph).length}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-orange-100 rounded-lg">
                                    <Target className="w-5 h-5 text-orange-600" />
                                </div>
                                <div>
                                    <div className="text-sm text-gray-600">平均相关性</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                        {documents.length > 0
                                            ? Math.round(documents.reduce((acc, doc) => acc + doc.relevanceScore, 0) / documents.length * 100)
                                            : 0}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        </div>
                    </div>

                    {/* 面包屑导航 */}
                    <div className="bg-white border-b border-gray-200 px-6 py-2">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            {getBreadcrumbs().map((crumb, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    {index > 0 && <ChevronRight className="w-3 h-3" />}
                                    <span className={index === getBreadcrumbs().length - 1 ? 'text-gray-900 font-medium' : 'hover:text-gray-900 cursor-pointer'}>
                                        {crumb.name}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>



                    {/* 搜索和筛选栏 */}
                    <div className="bg-white border-b border-gray-200 px-6 py-4">
                        <div className="flex items-center justify-between gap-4">
                            <div className="flex items-center gap-4">
                                <div className="relative flex-1 max-w-md">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="搜索文档..."
                                        className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                {/* 文档类型标签页 */}
                                <div className="flex bg-gray-100 rounded-lg p-1">
                                    <button
                                        onClick={() => setActiveTab('all')}
                                        className={`px-3 py-1 text-sm rounded-md transition-colors ${activeTab === 'all' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        全部
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('business')}
                                        className={`px-3 py-1 text-sm rounded-md transition-colors ${activeTab === 'business' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        业务文档
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('personal')}
                                        className={`px-3 py-1 text-sm rounded-md transition-colors ${activeTab === 'personal' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        个人知识库
                                    </button>
                                </div>

                                {/* 类别筛选 */}
                                <select
                                    value={selectedCategory}
                                    onChange={(e) => setSelectedCategory(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                >
                                    <option value="all">所有类别</option>
                                    {getAllCategories().map(category => (
                                        <option key={category} value={category}>{category}</option>
                                    ))}
                                </select>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Network className="w-4 h-4" />
                                    你的知识网络
                                </Button>
                                <Button 
                                    className="bg-purple-600 hover:bg-purple-700 text-white"
                                    onClick={() => setShowAddDialog(true)}
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    添加文档
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* 文档列表 */}
                    <div className="flex-1 overflow-auto p-6">
                        <DocumentList
                            documents={filteredDocuments}
                            getDocumentTypeIcon={getDocumentTypeIcon}
                            getPriorityColor={getPriorityColor}
                            getStatusIcon={getStatusIcon}
                        />
                    </div>
                </div>

                {/* 右侧导航栏 */}
                <div className={`bg-white border-l border-gray-200 flex flex-col transition-all duration-300 ${sidebarCollapsed ? 'w-16' : 'w-80'
                    }`}>
                    {/* 导航栏头部 */}
                    <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                                className="h-8 w-8 p-0"
                            >
                                {sidebarCollapsed ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                            </Button>
                            {!sidebarCollapsed && (
                                <div className="flex items-center gap-3">
                                    <div>
                                        <h2 className="text-lg font-semibold text-gray-900">文档导航</h2>
                                        <p className="text-xs text-gray-600">知识分类管理</p>
                                    </div>
                                    <img src="/spider-web.svg" alt="Spider Web" className="w-6 h-6" />
                                </div>
                            )}
                        </div>
                    </div>

                    {/* 导航树 */}
                    {!sidebarCollapsed && (
                        <div className="flex-1 overflow-auto p-4">
                            <div className="space-y-1">
                                {buildNavTree().map(node => renderNavNode(node))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 添加文档对话框 */}
            <AddDocumentDialog
                open={showAddDialog}
                onOpenChange={(open) => {
                    // 只允许通过内部的取消按钮关闭，禁用其他关闭方式
                    if (!open) {
                        // 可以添加确认逻辑
                        setShowAddDialog(false)
                    }
                }}
                onDocumentAdd={handleDocumentAdd}
            />
            
        </div>
    )
    
}



