import { useState } from "react"
import { SearchInput } from "@/components/SearchInput"
import { QueryResult } from "@/components/QueryResult"

// API 配置
const API_BASE_URL = 'http://localhost:8000'

// 查询结果类型定义
interface QueryResultType {
    answer: string
    sources: Array<{ content?: string } | string>
}

export default function HomePage() {
    const [selectedSearchType, setSelectedSearchType] = useState("深度搜索")
    const [thinkActive, setThinkActive] = useState(false)
    const [inputValue, setInputValue] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [queryResult, setQueryResult] = useState<QueryResultType | null>(null)
    const [error, setError] = useState<string | null>(null)
    const [hasQueried, setHasQueried] = useState(false)

    // 调用后端查询服务
    const handleSendQuery = async () => {
        if (!inputValue.trim()) return

        setIsLoading(true)
        setError(null)
        setHasQueried(true) // Mark that user has made a query

        try {
            const response = await fetch(`${API_BASE_URL}/api/rag/query?question=${encodeURIComponent(inputValue)}`)

            if (!response.ok) {
                throw new Error('查询失败')
            }

            const result = await response.json()

            // Transform the response to match expected structure
            // Handle different possible response structures
            let actualResult = result;

            // Check if response is nested under a property like 'generate_query_or_respond'
            if (result && typeof result === 'object') {
                const keys = Object.keys(result);
                if (keys.length === 1 && result[keys[0]]?.messages) {
                    actualResult = result[keys[0]];
                }
            }

            const transformedResult = {
                answer: actualResult.messages?.[0]?.content || actualResult.answer || result.answer || '未找到回答',
                sources: actualResult.sources || result.sources || []
            }

            setQueryResult(transformedResult)
            console.log('查询结果:', result)
            console.log('转换后结果:', transformedResult)

            // 清空输入框
            setInputValue("")
        } catch (err) {
            setError(err instanceof Error ? err.message : '查询出错')
            console.error('查询错误:', err)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="h-full flex flex-col">
            {hasQueried ? (
                // Layout after first query - input at bottom
                <>
                    {/* Main Content Area */}
                    <main className="flex-1 overflow-y-auto px-6 max-w-4xl mx-auto w-full pt-8">
                        <h1 className="text-3xl font-normal text-gray-900 mb-8">Agentic RAG</h1>
                        <QueryResult result={queryResult} error={error} />
                    </main>

                    {/* Fixed Input Section at Bottom */}
                    <div className="sticky bottom-0 z-10 bg-white border-t border-gray-200 pt-2 pb-3 sm:pt-4 sm:pb-6 w-full">
                        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-0">
                            <SearchInput
                                value={inputValue}
                                onChange={setInputValue}
                                onSubmit={handleSendQuery}
                                isLoading={isLoading}
                                selectedSearchType={selectedSearchType}
                                onSearchTypeChange={setSelectedSearchType}
                                thinkActive={thinkActive}
                                onThinkToggle={() => setThinkActive(!thinkActive)}
                            />
                        </div>
                    </div>

                    {/* Footer */}
                    <footer className="pb-8 px-6 text-center">
                        <p className="text-sm text-gray-500 max-w-2xl mx-auto">
                            向 3Stooges 发送消息即表示，您同意我们的{" "}
                            <a href="#" className="underline hover:text-gray-700">
                                条款
                            </a>
                            并已阅读我们的{" "}
                            <a href="#" className="underline hover:text-gray-700">
                                隐私政策
                            </a>
                            。
                        </p>
                    </footer>
                </>
            ) : (
                // Initial layout - input centered
                <>
                    {/* Main Content Area - Centered */}
                    <main className="flex-1 flex flex-col items-center justify-center px-6 max-w-4xl mx-auto w-full">
                        <h1 className="text-5xl font-normal text-gray-900 mb-12">Agentic RAG</h1>

                        <div className="w-full max-w-4xl">
                            <SearchInput
                                value={inputValue}
                                onChange={setInputValue}
                                onSubmit={handleSendQuery}
                                isLoading={isLoading}
                                selectedSearchType={selectedSearchType}
                                onSearchTypeChange={setSelectedSearchType}
                                thinkActive={thinkActive}
                                onThinkToggle={() => setThinkActive(!thinkActive)}
                            />
                        </div>
                    </main>

                    {/* Footer */}
                    <footer className="pb-8 px-6 text-center">
                        <p className="text-sm text-gray-500 max-w-2xl mx-auto">
                            向 3Stooges 发送消息即表示，您同意我们的{" "}
                            <a href="#" className="underline hover:text-gray-700">
                                条款
                            </a>
                            并已阅读我们的{" "}
                            <a href="#" className="underline hover:text-gray-700">
                                隐私政策
                            </a>
                            。
                        </p>
                    </footer>
                </>
            )}
        </div>
    )
}