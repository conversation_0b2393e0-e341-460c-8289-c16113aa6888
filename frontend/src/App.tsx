import { useState } from "react"
import logo from "@/assets/logo.svg"
import { But<PERSON> } from "@/components/ui/button"
import {
  HelpCircle,
  Search,
  MessageSquare,
  FileText,
  CheckSquare,
  FolderOpen,
  History,
  ChevronLeft,
  ChevronRight,
  User,
  Home,
} from "lucide-react"

// Import pages
import HomePage from "@/pages/HomePage"
import ChatPage from "@/pages/ChatPage"
import DocumentsPage from "@/pages/DocumentsPage"
import TasksPage from "@/pages/TasksPage"
import ProjectsPage from "@/pages/ProjectsPage"
import HistoryPage from "@/pages/HistoryPage"

type PageType = 'home' | 'chat' | 'documents' | 'tasks' | 'projects' | 'history'

export default function ChatGPTHomepage() {
  const [collapsed, setCollapsed] = useState(false)
  const [currentPage, setCurrentPage] = useState<PageType>('home')

  // Function to render current page
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'chat':
        return <ChatPage />
      case 'documents':
        return <DocumentsPage />
      case 'tasks':
        return <TasksPage />
      case 'projects':
        return <ProjectsPage />
      case 'history':
        return <HistoryPage />
      default:
        return <HomePage />
    }
  }
  return (
    <div className="min-h-screen h-screen bg-white flex">
      {/* Left Sidebar */}
      <aside className={`h-screen ${collapsed ? "w-20" : "w-64"} bg-gray-50 border-r border-gray-200 flex flex-col transition-all duration-200`}>
        {/* Sidebar Header with ChatGPT Logo */}
        <div className="p-4 border-b border-gray-200 flex items-center gap-2 justify-center">
          <img src={logo} alt="logo" className={`transition-all duration-200 ${collapsed ? "w-8 h-8" : "w-10 h-10"}`} />
          {!collapsed && (
            <span className="text-xl font-semibold text-gray-900">3Stooges</span>
          )}
        </div>

        {/* Sidebar Header */}
        {!collapsed && (
          <div className="px-4 pb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                placeholder="搜索 ⌘K"
                className="pl-10 h-10 w-full bg-white border border-gray-200 rounded-lg text-sm px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
        )}

        {/* Navigation Menu */}
        <nav className="flex-1 px-2">
          <div className={`space-y-1 flex flex-col items-center ${collapsed ? 'justify-center' : ''}`}>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('home')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'home' ? 'bg-gray-200' : ''}`}
            >
              <Home className="w-4 h-4" />
              {!collapsed && <span className="ml-3">首页</span>}
            </Button>
            {/* <Button
              variant="ghost"
              onClick={() => setCurrentPage('chat')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'chat' ? 'bg-gray-200' : ''}`}
            >
              <MessageSquare className="w-4 h-4" />
              {!collapsed && <span className="ml-3">检索</span>}
            </Button> */}
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('documents')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'documents' ? 'bg-gray-200' : ''}`}
            >
              <FileText className="w-4 h-4" />
              {!collapsed && <span className="ml-3">上下文</span>}
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('tasks')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'tasks' ? 'bg-gray-200' : ''}`}
            >
              <CheckSquare className="w-4 h-4" />
              {!collapsed && <span className="ml-3">任务</span>}
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('projects')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'projects' ? 'bg-gray-200' : ''}`}
            >
              <FolderOpen className="w-4 h-4" />
              {!collapsed && <span className="ml-3">项目</span>}
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('history')}
              className={`h-10 w-10 flex items-center justify-center ${collapsed ? 'mx-auto' : 'w-full justify-start px-3'} text-gray-700 hover:bg-gray-200 ${currentPage === 'history' ? 'bg-gray-200' : ''}`}
            >
              <History className="w-4 h-4" />
              {!collapsed && <span className="ml-3">历史记录</span>}
            </Button>
          </div>
        </nav>

        {/* Sidebar Footer */}
        <div className={`p-2 border-t border-gray-200 ${collapsed ? "flex flex-col items-center justify-center flex-1 gap-2" : "flex items-center justify-between"}`}>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600" />
            </div>
            {!collapsed && <span className="text-sm text-gray-700">用户名</span>}
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8 mt-2" onClick={() => setCollapsed(v => !v)}>
            {collapsed ? <ChevronRight className="w-4 h-4 text-gray-600" /> : <ChevronLeft className="w-4 h-4 text-gray-600" />}
          </Button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        {/* <header className="flex items-center justify-end px-6 py-4">
          <div className="flex items-center gap-3">
            <Button variant="default" className="bg-black text-white hover:bg-gray-800 rounded-full px-6">
              登录
            </Button>
            <Button variant="ghost" size="icon" className="rounded-full">
              <HelpCircle className="w-5 h-5 text-gray-600" />
            </Button>
          </div>
        </header> */}

        {/* Main Content Area - Render Current Page */}
        <main className="flex-1 w-full">
          {renderCurrentPage()}
        </main>
      </div>
    </div>
  )
}
