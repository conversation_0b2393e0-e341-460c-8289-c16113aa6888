## Agentic RAG

### 项目简介

Agentic RAG 是一个基于 LangGraph 和 LangChain 的智能检索增强生成（RAG）工作流系统，支持模块化架构、FastAPI 服务、本地开发和可视化监控。

---

### 技术栈

- Python 3.12+
- LangGraph & LangChain（OpenAI, Ollama, Community）
- FastAPI & Uvicorn
- uv 虚拟环境管理

---

### 项目结构

```
├── src/
│   ├── __init__.py
│   ├── api.py              # FastAPI 服务
│   ├── config.py           # 配置管理
│   ├── document_processor.py # 文档处理
│   ├── models.py           # 模型配置
│   ├── prompts.py          # 提示词模板
│   ├── rag_service.py      # RAG 服务封装
│   ├── workflow.py         # 工作流构建
│   └── workflow_nodes.py   # 工作流节点
├── main.py                 # 原始版本
├── main_refactored.py      # 重构版本
├── test_client.py          # 测试客户端
└── .env                    # 环境变量
```

---

### 环境准备

使用 [uv](https://github.com/astral-sh/uv) 管理依赖：

```bash
uv sync
```

---

### 环境变量

在项目根目录创建 `.env` 文件：

```env
OPENAI_API_KEY=你的OpenAI Key
OPENAI_API_BASE=你的OpenAI Base URL
OPENAI_MODEL_NAME=你的模型名
OPENAI_EMB_MODEL_NAME=你的Embedding模型名
```

---

### 运行方式

#### 1. FastAPI 服务（推荐）

启动 REST API 服务：

```bash
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

服务启动后访问：
- API 文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

**API 调用示例：**

```bash
# 查询接口
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "请总结一下Lilian Weng关于幻觉的观点",
    "stream": false
  }'

# 流式查询
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What does Lilian Weng say about hallucination?",
    "stream": true
  }'

# 重新加载文档
curl -X POST "http://localhost:8000/reload" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": ["https://example.com/new-doc"]
  }'
```

#### 2. 直接测试

运行重构版本：

```bash
uv run python main_refactored.py
```

#### 3. LangGraph 开发服务器

```bash
# check the langgraph.json
langgraph dev
```

langsmith可视化界面：
```
https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
```


#### 测试客户端

使用内置测试客户端：

```bash
uv run python test_client.py
```

---

### 本地 Ollama 配置

如使用本地 Ollama 嵌入服务：

```python
# 在 src/config.py 中配置
embed = OllamaEmbeddings(
    model="snowflake-arctic-embed2:latest"
)
```

---

### API 接口说明

#### POST /query
查询 RAG 系统

**请求参数：**
- `question` (string): 用户问题
- `stream` (boolean, 可选): 是否流式返回，默认 false

**响应：**
- 非流式：`{"answer": "回答内容", "sources": null}`
- 流式：Server-Sent Events 格式

#### POST /reload
重新加载文档

**请求参数：**
- `urls` (array): 新的文档 URL 列表

#### GET /health
健康检查接口

---


---

### 开发说明

- **配置管理**：修改 `src/config.py`
- **提示词**：修改 `src/prompts.py`
- **工作流逻辑**：修改 `src/workflow_nodes.py`
- **文档处理**：修改 `src/document_processor.py`

---

### 参考文档

- [LangGraph 官方文档](https://langchain-ai.github.io/langgraph/)
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [LangChain 官方文档](https://python.langchain.com/)
